#!/usr/bin/env python3
"""
Test script to reproduce the REST API long text hang issue.
This will help us confirm the exact path that causes the hang.
"""

import asyncio
import json
import requests
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def test_rest_api_hang():
    """Test the REST API with long text to reproduce the hang."""
    
    print("🔍 Testing REST API Long Text Hang Issue")
    print("=" * 50)
    
    # Create a very long text that should trigger the issue
    long_text = "This is a test memory with lots of content. " * 200  # ~9000 chars
    print(f"Test text length: {len(long_text)} characters")
    
    # Test the REST API endpoint
    api_url = "http://localhost:8000/api/v1/memories/"
    
    payload = {
        "text": long_text,
        "metadata": {"test": "chunking_issue"},
        "app": "test_app"
    }
    
    print(f"\n📡 Testing REST API POST to {api_url}")
    print(f"Payload text length: {len(payload['text'])} chars")
    
    try:
        print("🚨 DANGER: This might hang if the issue exists!")
        print("⏰ Setting 30-second timeout to prevent infinite hang...")
        
        # Add timeout to prevent infinite hang
        response = requests.post(
            api_url, 
            json=payload,
            timeout=30,  # 30 second timeout
            headers={"Content-Type": "application/json"}
        )
        
        print(f"✅ Response Status: {response.status_code}")
        print(f"📄 Response Body: {response.text[:500]}...")
        
        if response.status_code == 400:
            print("✅ Validation correctly rejected long text")
        elif response.status_code == 200:
            print("⚠️  Long text was accepted - this could indicate the issue!")
        else:
            print(f"🤔 Unexpected status code: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("🚨 CONFIRMED: REST API TIMED OUT!")
        print("This confirms the hang issue exists in the REST API")
        print("The text validation is either:")
        print("1. Not working properly")
        print("2. Being bypassed") 
        print("3. Allowing text that's too long for mem0")
        
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API (is it running?)")
        print("To test this properly, start the API server first:")
        print("cd api && python -m uvicorn main:app --reload")
        
    except Exception as e:
        print(f"🔥 Unexpected error: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 DIAGNOSIS:")
    print("1. The REST API has validation but may not be working correctly")
    print("2. Long text gets sent directly to mem0 without chunking")
    print("3. mem0 hangs when processing very long text")
    print("4. SOLUTION: Implement automatic chunking in both MCP and REST APIs")

if __name__ == "__main__":
    asyncio.run(test_rest_api_hang())