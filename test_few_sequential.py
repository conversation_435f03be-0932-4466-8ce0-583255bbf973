#!/usr/bin/env python3
"""
Test script to test 3 memories sequentially with detailed logging.
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://************:8765"
USER_ID = "aungheinaye"

# Sample memories
MEMORIES = [
    "User prefers using async/await syntax over callbacks (test 1)",
    "Project uses TypeScript with strict mode enabled (test 2)", 
    "<PERSON><PERSON><PERSON> likes to use descriptive variable names (test 3)"
]

async def test_memory(session, memory_text, memory_id):
    """Test creating a single memory."""
    url = f"{BASE_URL}/api/v1/memories"
    payload = {"text": memory_text}
    
    print(f"\n[{memory_id}] 🚀 Starting request: {memory_text[:50]}...")
    
    try:
        start_time = time.time()
        async with session.post(url, json=payload, timeout=30) as response:
            duration = time.time() - start_time
            
            print(f"[{memory_id}] ⏱️  Response received in {duration:.3f}s")
            print(f"[{memory_id}] 📊 Status: {response.status}")
            
            if response.status in [200, 201, 422]:
                result = await response.json()
                print(f"[{memory_id}] ✅ Success: {json.dumps(result, indent=2)}")
                return True, duration, result
            else:
                text = await response.text()
                print(f"[{memory_id}] ❌ Error: {text}")
                return False, duration, text
                
    except asyncio.TimeoutError:
        duration = time.time() - start_time
        print(f"[{memory_id}] ⏰ Timeout after {duration:.3f}s")
        return False, duration, "Timeout"
    except Exception as e:
        duration = time.time() - start_time
        print(f"[{memory_id}] 💥 Exception after {duration:.3f}s: {e}")
        return False, duration, str(e)

async def main():
    print(f"🚀 Testing 3 Sequential Memories")
    print(f"   Target: {BASE_URL}")
    print(f"   Start time: {datetime.now().isoformat()}")
    print("=" * 80)
    
    async with aiohttp.ClientSession() as session:
        results = []
        
        for i, memory_text in enumerate(MEMORIES):
            result = await test_memory(session, memory_text, i+1)
            results.append(result)
            
            # Small delay between requests
            print(f"[{i+1}] 😴 Waiting 2 seconds before next request...")
            await asyncio.sleep(2)
        
        # Summary
        print("\n" + "=" * 80)
        print("📊 SUMMARY")
        successful = sum(1 for r in results if r[0])
        failed = len(results) - successful
        
        print(f"   Total: {len(results)}")
        print(f"   Successful: {successful}")
        print(f"   Failed: {failed}")
        
        if failed == 0:
            print("✅ All requests completed successfully!")
        else:
            print(f"❌ {failed} requests failed")
        
        print(f"   End time: {datetime.now().isoformat()}")

if __name__ == "__main__":
    asyncio.run(main())
