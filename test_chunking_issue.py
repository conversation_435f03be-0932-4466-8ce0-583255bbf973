#!/usr/bin/env python3
"""
Test script to reproduce the long text memory chunking issue.
This script will help us validate our diagnosis of the problem.
"""

import asyncio
import logging
import os
import sys

# Add the API path to import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

from app.mcp_server import add_memories, validate_text_length, get_max_text_length_from_config
from app.mcp_server import user_id_var, client_name_var

# Configure logging to see diagnostic messages
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def test_long_text_issue():
    """Test the long text issue to confirm our diagnosis."""
    
    print("🔍 Testing Long Text Memory Issue Diagnosis")
    print("=" * 50)
    
    # Test 1: Check current text length validation
    print("\n📝 Test 1: Text Length Validation")
    max_length = get_max_text_length_from_config()
    print(f"Current max_text_length setting: {max_length}")
    
    # Create a long text that exceeds the limit
    long_text = "This is a test memory. " * 100  # Should be around 2300+ chars
    print(f"Test text length: {len(long_text)} characters")
    
    # Test validation function
    is_valid, message = validate_text_length(long_text)
    print(f"Validation result: {is_valid}")
    print(f"Validation message: {message}")
    
    # Test 2: Check if chunking logic exists
    print("\n🔨 Test 2: Chunking Logic Check")
    if "chunk" in message.lower():
        print("✅ Warning mentions chunking, but let's check if it's implemented...")
    else:
        print("❌ No chunking mentioned in validation")
    
    # Test 3: Try to call add_memories with long text (safely)
    print("\n⚠️  Test 3: Simulated add_memories Call")
    print("Setting up context variables...")
    
    # Set context variables (simulating an MCP call)
    user_token = user_id_var.set("test_user")
    client_token = client_name_var.set("test_client")
    
    try:
        print(f"Calling add_memories with {len(long_text)} character text...")
        print("This should show the warning but then try to process the long text...")
        
        # This will show us exactly where the hang occurs
        result = await add_memories(long_text)
        print(f"Result: {result}")
        
    except Exception as e:
        print(f"Exception occurred: {e}")
        print("This confirms our diagnosis - the system tries to process long text without chunking")
    finally:
        # Clean up context
        user_id_var.reset(user_token)
        client_name_var.reset(client_token)
    
    print("\n" + "=" * 50)
    print("🎯 DIAGNOSIS SUMMARY:")
    print("1. ✅ Text length validation exists and warns about long text")
    print("2. ❌ No actual chunking implementation found")
    print("3. ⚠️  Long text gets sent directly to mem0, causing hangs")
    print("4. 🔧 SOLUTION: Implement automatic text chunking in add_memories()")

if __name__ == "__main__":
    asyncio.run(test_long_text_issue())