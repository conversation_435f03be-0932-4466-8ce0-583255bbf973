#!/usr/bin/env python3
"""
Debug script to investigate the mem0 empty results issue.
"""

import sys
import os
import json
import logging

# Add the api directory to the path
sys.path.insert(0, '/home/<USER>/memory-master-v2/api')

# Set up logging
logging.basicConfig(level=logging.INFO)

def debug_mem0_issue():
    """Debug the mem0 empty results issue."""
    try:
        print("🔍 Debugging mem0 empty results issue...")
        
        # Import required modules
        from app.utils.memory import get_memory_client, get_default_memory_config
        from app.database import SessionLocal
        from app.models import Config as ConfigModel
        
        print("\n1. Checking database configuration...")
        db = SessionLocal()
        try:
            config_record = db.query(ConfigModel).filter(ConfigModel.key == 'main').first()
            if config_record:
                print("✅ Config found in database")
                print(f"Config keys: {list(config_record.value.keys())}")
                if 'mem0' in config_record.value:
                    mem0_config = config_record.value['mem0']
                    print(f"Mem0 LLM provider: {mem0_config.get('llm', {}).get('provider', 'NOT SET')}")
                    print(f"Mem0 embedder provider: {mem0_config.get('embedder', {}).get('provider', 'NOT SET')}")
            else:
                print("❌ No config found in database")
        finally:
            db.close()
        
        print("\n2. Checking environment variables...")
        openai_key = os.getenv('OPENAI_API_KEY')
        if openai_key:
            print(f"✅ OPENAI_API_KEY is set (length: {len(openai_key)})")
        else:
            print("❌ OPENAI_API_KEY is not set")
        
        print("\n3. Getting default memory config...")
        default_config = get_default_memory_config()
        print(f"Default config structure: {list(default_config.keys())}")
        
        print("\n4. Attempting to initialize memory client...")
        memory_client = get_memory_client()
        
        if memory_client:
            print("✅ Memory client initialized successfully")
            print(f"Client type: {type(memory_client)}")
            
            # Check if client has the expected methods
            if hasattr(memory_client, 'add'):
                print("✅ Client has 'add' method")
            else:
                print("❌ Client missing 'add' method")
            
            if hasattr(memory_client, 'config'):
                print(f"Client config type: {type(memory_client.config)}")
            
            print("\n5. Testing a simple memory addition...")
            try:
                test_content = "This is a test memory for debugging"
                test_metadata = {"test": True, "debug": True}
                
                print(f"Adding test memory: '{test_content[:50]}...'")
                result = memory_client.add(test_content, metadata=test_metadata)
                
                print(f"Raw result type: {type(result)}")
                print(f"Raw result: {result}")
                
                if isinstance(result, dict):
                    print(f"Result keys: {list(result.keys())}")
                    if 'results' in result:
                        results = result['results']
                        print(f"Results type: {type(results)}")
                        print(f"Results length: {len(results) if isinstance(results, list) else 'N/A'}")
                        if isinstance(results, list) and len(results) > 0:
                            print(f"First result: {results[0]}")
                        else:
                            print("❌ Results list is empty!")
                            
                            # Let's check what mem0 is actually doing
                            print("\n6. Investigating mem0 internals...")
                            if hasattr(memory_client, 'llm'):
                                print(f"LLM provider: {memory_client.llm}")
                            if hasattr(memory_client, 'embedder'):
                                print(f"Embedder provider: {memory_client.embedder}")
                            if hasattr(memory_client, 'vector_store'):
                                print(f"Vector store: {memory_client.vector_store}")
                    else:
                        print("❌ No 'results' key in response")
                else:
                    print("❌ Result is not a dictionary")
                    
            except Exception as e:
                print(f"❌ Error during memory addition: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("❌ Failed to initialize memory client")
            
    except Exception as e:
        print(f"❌ Debug script failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_mem0_issue()
