#!/usr/bin/env python3
"""
Test script to test 5 memories concurrently to identify concurrency issues.
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://************:8765"
USER_ID = "aungheinaye"

# Sample memories
MEMORIES = [
    "User prefers using async/await syntax over callbacks (concurrent test 1)",
    "Project uses TypeScript with strict mode enabled (concurrent test 2)", 
    "<PERSON>elo<PERSON> likes to use descriptive variable names (concurrent test 3)",
    "Team follows ESLint rules with no semicolons (concurrent test 4)",
    "User prefers arrow functions over traditional functions (concurrent test 5)"
]

async def test_memory(session, memory_text, memory_id):
    """Test creating a single memory."""
    url = f"{BASE_URL}/api/v1/memories"
    payload = {"text": memory_text}
    
    print(f"[{memory_id}] 🚀 Starting concurrent request...")
    
    try:
        start_time = time.time()
        async with session.post(url, json=payload, timeout=30) as response:
            duration = time.time() - start_time
            
            print(f"[{memory_id}] ⏱️  Response in {duration:.3f}s, Status: {response.status}")
            
            if response.status in [200, 201, 422]:
                result = await response.json()
                print(f"[{memory_id}] ✅ Success")
                return True, duration, result
            else:
                text = await response.text()
                print(f"[{memory_id}] ❌ Error: {text[:100]}")
                return False, duration, text
                
    except asyncio.TimeoutError:
        duration = time.time() - start_time
        print(f"[{memory_id}] ⏰ Timeout after {duration:.3f}s")
        return False, duration, "Timeout"
    except Exception as e:
        duration = time.time() - start_time
        print(f"[{memory_id}] 💥 Exception after {duration:.3f}s: {e}")
        return False, duration, str(e)

async def main():
    print(f"🚀 Testing 5 Concurrent Memories")
    print(f"   Target: {BASE_URL}")
    print(f"   Start time: {datetime.now().isoformat()}")
    print("=" * 80)
    
    async with aiohttp.ClientSession() as session:
        # Create all tasks
        tasks = []
        for i, memory_text in enumerate(MEMORIES):
            task = test_memory(session, memory_text, i+1)
            tasks.append(task)
        
        # Run all tasks concurrently
        print("🏃 Running all requests concurrently...")
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_duration = time.time() - start_time
        
        # Process results
        successful = 0
        failed = 0
        timeouts = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"[{i+1}] 💥 Task exception: {result}")
                failed += 1
            else:
                success, duration, response = result
                if success:
                    successful += 1
                else:
                    failed += 1
                    if "Timeout" in str(response):
                        timeouts += 1
        
        # Summary
        print("\n" + "=" * 80)
        print("📊 CONCURRENT TEST SUMMARY")
        print(f"   Total requests: {len(MEMORIES)}")
        print(f"   Successful: {successful}")
        print(f"   Failed: {failed}")
        print(f"   Timeouts: {timeouts}")
        print(f"   Success rate: {(successful/len(MEMORIES))*100:.1f}%")
        print(f"   Total duration: {total_duration:.3f}s")
        
        if timeouts > 0:
            print(f"\n❌ CONCURRENCY ISSUE DETECTED:")
            print(f"   {timeouts} requests timed out during concurrent execution")
            print(f"   This suggests a database connection pool or resource contention issue")
        else:
            print(f"\n✅ All concurrent requests completed successfully!")
        
        print(f"   End time: {datetime.now().isoformat()}")

if __name__ == "__main__":
    asyncio.run(main())
