#!/usr/bin/env python3
"""
Simple integration test for concurrent operation handling implementation.
This script tests the basic functionality without requiring the full application setup.
"""

import sys
import os
import threading
import time
import queue
from unittest.mock import Mock, patch

# Add the API directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'api'))

def test_basic_functionality():
    """Test basic concurrent operation functionality."""
    print("Testing basic concurrent operation functionality...")
    
    try:
        # Import the memory singleton
        from app.utils.memory import MemoryClientSingleton
        
        print("✓ Successfully imported MemoryClientSingleton")
        
        # Test singleton creation
        singleton1 = MemoryClientSingleton()
        singleton2 = MemoryClientSingleton()
        
        assert singleton1 is singleton2, "Singleton instances should be identical"
        print("✓ Singleton pattern working correctly")
        
        # Test queue status
        status = singleton1.get_operation_queue_status()
        assert isinstance(status, dict), "Status should be a dictionary"
        assert 'queue_size' in status, "Status should contain queue_size"
        print("✓ Queue status reporting working")
        
        # Test async operation without client (should fail gracefully)
        success, message = singleton1.add_memory_async("test content")
        assert not success, "Operation should fail without initialized client"
        assert "not initialized" in message.lower(), "Error message should mention initialization"
        print("✓ Graceful handling of operations without client")
        
        print("All basic tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_worker_thread_management():
    """Test worker thread start/stop functionality."""
    print("\nTesting worker thread management...")
    
    try:
        from app.utils.memory import MemoryClientSingleton
        
        # Reset singleton for clean test
        MemoryClientSingleton._instance = None
        MemoryClientSingleton._worker_thread = None
        MemoryClientSingleton._worker_running = False
        
        singleton = MemoryClientSingleton()
        
        # Initially no worker should be running
        status = singleton.get_operation_queue_status()
        assert not status['worker_running'], "Worker should not be running initially"
        print("✓ Initial worker state correct")
        
        # Start worker
        singleton._start_worker()
        
        # Check worker is running
        status = singleton.get_operation_queue_status()
        assert status['worker_running'], "Worker should be running after start"
        print("✓ Worker thread starts correctly")
        
        # Stop worker
        singleton._stop_worker()
        
        # Wait a bit for thread to stop
        time.sleep(0.2)
        
        # Check worker is stopped
        status = singleton.get_operation_queue_status()
        assert not status['worker_running'], "Worker should be stopped"
        print("✓ Worker thread stops correctly")
        
        print("Worker thread management tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Worker thread test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_concurrent_access():
    """Test concurrent access to singleton."""
    print("\nTesting concurrent access...")
    
    try:
        from app.utils.memory import MemoryClientSingleton
        
        # Reset singleton
        MemoryClientSingleton._instance = None
        
        instances = []
        errors = []
        
        def get_instance():
            try:
                instances.append(MemoryClientSingleton())
            except Exception as e:
                errors.append(str(e))
        
        # Create multiple threads
        threads = [threading.Thread(target=get_instance) for _ in range(10)]
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join()
        
        # Check results
        assert len(errors) == 0, f"No errors should occur: {errors}"
        assert len(instances) == 10, "Should have 10 instances"
        
        # All instances should be the same object
        first_instance = instances[0]
        for instance in instances[1:]:
            assert instance is first_instance, "All instances should be identical"
        
        print("✓ Concurrent access test passed")
        return True
        
    except Exception as e:
        print(f"✗ Concurrent access test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("Running concurrent operation implementation tests...\n")
    
    tests = [
        test_basic_functionality,
        test_worker_thread_management,
        test_concurrent_access
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()  # Add spacing between tests
    
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Concurrent operation implementation is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
