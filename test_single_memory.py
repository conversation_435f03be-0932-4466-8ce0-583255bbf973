#!/usr/bin/env python3
"""
Test script to test a single memory creation via REST API.
This will help us verify if the fix works for individual requests.
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://************:8765"
USER_ID = "aungheinaye"

async def test_single_memory():
    """Test creating a single memory via the REST API."""
    url = f"{BASE_URL}/api/v1/memories"
    
    # REST API payload
    payload = {
        "text": "User prefers using async/await syntax over callbacks (single test)"
    }
    
    print(f"🚀 Testing single memory creation")
    print(f"   Target: {BASE_URL}")
    print(f"   User: {USER_ID}")
    print(f"   Text: {payload['text']}")
    print(f"   Start time: {datetime.now().isoformat()}")
    print("-" * 80)
    
    try:
        start_time = time.time()
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, timeout=30) as response:
                duration = time.time() - start_time
                
                print(f"Response status: {response.status}")
                print(f"Response time: {duration:.3f}s")
                
                if response.status in [200, 201]:
                    result = await response.json()
                    print(f"✅ SUCCESS: Memory created successfully")
                    print(f"Response: {json.dumps(result, indent=2)}")
                    return True
                elif response.status == 422:
                    result = await response.json()
                    print(f"⚠️  EXPECTED: Memory rejected as non-memorable")
                    print(f"Response: {json.dumps(result, indent=2)}")
                    return True  # This is actually success - the fix is working!
                else:
                    text = await response.text()
                    print(f"❌ ERROR: HTTP {response.status}")
                    print(f"Response: {text}")
                    return False
                    
    except asyncio.TimeoutError:
        duration = time.time() - start_time
        print(f"❌ TIMEOUT after {duration:.3f}s")
        return False
    except Exception as e:
        duration = time.time() - start_time
        print(f"❌ EXCEPTION after {duration:.3f}s: {e}")
        return False

async def main():
    success = await test_single_memory()
    
    print("-" * 80)
    if success:
        print("✅ Test completed successfully!")
        print("The fix is working - requests are no longer hanging.")
    else:
        print("❌ Test failed - requests are still hanging or erroring.")
    
    print(f"End time: {datetime.now().isoformat()}")

if __name__ == "__main__":
    asyncio.run(main())
