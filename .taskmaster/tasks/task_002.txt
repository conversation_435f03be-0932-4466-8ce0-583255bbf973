# Task ID: 2
# Title: Decouple Configuration Management from Client Lifecycle
# Status: done
# Dependencies: 1
# Priority: high
# Description: Separate configuration loading from client initialization to prevent unnecessary client reinitialization when configuration changes.
# Details:
Modify the memory client to handle configuration changes without recreating the client:
1. Add a configuration hash mechanism to detect changes
2. Categorize configuration parameters into critical (requires reinitialization) and non-critical
3. Only reinitialize for critical changes (vector_store config, API keys, provider changes)
4. Cache non-critical changes (custom_instructions, temperature, max_tokens)

```python
class MemoryClientSingleton:
    # ... existing singleton code ...
    _config_hash = None
    
    def update_config(self, new_config):
        # Generate hash of critical config parameters
        critical_config = {
            'vector_store': new_config.get('vector_store'),
            'api_key': new_config.get('api_key'),
            'provider': new_config.get('provider')
        }
        config_hash = hash(frozenset(critical_config.items()))
        
        # Only reinitialize if critical config changed
        if self._config_hash != config_hash:
            self._config_hash = config_hash
            self._reinitialize_client(new_config)
        else:
            # Update non-critical config without reinitialization
            self._update_non_critical_config(new_config)
```

# Test Strategy:
1. Write unit tests for configuration change detection:
   - Test that critical config changes trigger reinitialization
   - Test that non-critical changes don't trigger reinitialization
   - Verify config hash calculation works correctly
2. Integration test to ensure vector store connection persists across non-critical config changes
3. Test with various configuration scenarios to verify correct behavior
