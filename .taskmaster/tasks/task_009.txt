# Task ID: 9
# Title: Implement Vector Store Connectivity Monitoring
# Status: done
# Dependencies: 3, 7
# Priority: low
# Description: Add monitoring for vector store connectivity to detect and report issues proactively.
# Details:
Implement vector store connectivity monitoring:
1. Add periodic health checks for vector store connectivity
2. Implement a background monitoring thread
3. Add alerting for connectivity issues
4. Track and report connectivity metrics

```python
class MemoryClientSingleton:
    # ... existing singleton code ...
    _monitoring_thread = None
    _monitoring_interval = 300  # seconds
    _connectivity_history = collections.deque(maxlen=100)  # Store last 100 checks
    
    def _start_monitoring(self):
        """Start background monitoring thread."""
        if self._monitoring_thread is None or not self._monitoring_thread.is_alive():
            self._monitoring_thread = threading.Thread(target=self._monitor_connectivity, daemon=True)
            self._monitoring_thread.start()
    
    def _monitor_connectivity(self):
        """Monitor vector store connectivity periodically."""
        while True:
            try:
                is_connected = self._check_vector_store_connectivity()
                self._connectivity_history.append((time.time(), is_connected))
                
                if not is_connected and not self._degraded_mode:
                    self._enter_degraded_mode()
                elif is_connected and self._degraded_mode:
                    self._exit_degraded_mode()
                    
                # Calculate uptime percentage
                if len(self._connectivity_history) > 10:
                    uptime = sum(1 for _, connected in self._connectivity_history if connected) / len(self._connectivity_history)
                    logging.info(f"Vector store uptime: {uptime:.2%}")
            except Exception as e:
                logging.error(f"Monitoring error: {str(e)}")
            
            time.sleep(self._monitoring_interval)
    
    def _check_vector_store_connectivity(self):
        """Check if vector store is accessible."""
        try:
            # Perform lightweight operation to check connectivity
            # For example, try to retrieve a known memory or check status
            return self._client.check_vector_store()
        except Exception:
            return False
    
    def get_connectivity_status(self):
        """Get vector store connectivity status."""
        if not self._connectivity_history:
            return {"status": "unknown", "uptime": None}
            
        is_connected = self._connectivity_history[-1][1] if self._connectivity_history else False
        uptime = sum(1 for _, connected in self._connectivity_history if connected) / len(self._connectivity_history) if self._connectivity_history else 0
        
        return {
            "status": "connected" if is_connected else "disconnected",
            "uptime": uptime,
            "degraded_mode": self._degraded_mode,
            "last_check": self._connectivity_history[-1][0] if self._connectivity_history else None
        }
```

# Test Strategy:
1. Write unit tests for connectivity monitoring:
   - Test connectivity check functionality
   - Test monitoring thread behavior
   - Test status reporting
   - Test uptime calculation
2. Integration test with simulated connectivity issues
3. Test automatic mode switching based on connectivity
