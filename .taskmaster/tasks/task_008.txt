# Task ID: 8
# Title: Enhance Error Logging and Status Reporting
# Status: done
# Dependencies: 4, 5
# Priority: medium
# Description: Implement comprehensive error logging and status reporting for memory operations to improve debugging and visibility.
# Details:
Enhance error logging and status reporting in the memory system:
1. Add structured logging for all memory operations
2. Implement detailed error classification
3. Add operation timing metrics
4. Create a status reporting mechanism for memory operations

```python
import logging
import time
from enum import Enum

class MemoryOperationStatus(Enum):
    SUCCESS = "success"
    PARTIAL_SUCCESS = "partial_success"
    FAILURE = "failure"
    DEGRADED = "degraded"

class MemoryOperationResult:
    def __init__(self, status, message, data=None, duration_ms=None):
        self.status = status
        self.message = message
        self.data = data or {}
        self.duration_ms = duration_ms
        self.timestamp = time.time()
    
    def to_dict(self):
        return {
            "status": self.status.value,
            "message": self.message,
            "data": self.data,
            "duration_ms": self.duration_ms,
            "timestamp": self.timestamp
        }

def log_memory_operation(operation_name):
    """Decorator to log memory operations with timing and status."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                end_time = time.time()
                duration_ms = int((end_time - start_time) * 1000)
                
                # Validate result and create operation result
                success, message = validate_mem0_response(result, operation_name)
                status = MemoryOperationStatus.SUCCESS if success else MemoryOperationStatus.FAILURE
                op_result = MemoryOperationResult(status, message, result, duration_ms)
                
                # Log operation result
                log_level = logging.INFO if success else logging.ERROR
                logging.log(log_level, f"{operation_name}: {message} ({duration_ms}ms)")
                
                return op_result
            except Exception as e:
                end_time = time.time()
                duration_ms = int((end_time - start_time) * 1000)
                op_result = MemoryOperationResult(
                    MemoryOperationStatus.FAILURE,
                    f"{operation_name} failed: {str(e)}",
                    {"error": str(e)},
                    duration_ms
                )
                logging.error(f"{operation_name} failed: {str(e)} ({duration_ms}ms)", exc_info=True)
                return op_result
        return wrapper
    return decorator

@log_memory_operation("add_memory")
def add_memory(content, metadata):
    return mem0_client.add_memory(content, metadata)
```

# Test Strategy:
1. Write unit tests for logging and status reporting:
   - Test successful operation logging
   - Test failed operation logging
   - Test operation result structure
   - Test timing information accuracy
2. Integration test to verify logs contain all necessary information
3. Test error classification to ensure errors are properly categorized
