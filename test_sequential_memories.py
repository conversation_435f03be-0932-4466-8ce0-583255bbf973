#!/usr/bin/env python3
"""
Test script to test memory creation sequentially (one at a time).
This will help us verify if the issue is with concurrency or individual requests.
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://************:8765"
USER_ID = "aungheinaye"
NUM_MEMORIES = 5  # Test with just 5 memories sequentially

# Sample short text memories
SAMPLE_MEMORIES = [
    "User prefers using async/await syntax over callbacks",
    "Project uses TypeScript with strict mode enabled",
    "<PERSON><PERSON><PERSON> likes to use descriptive variable names",
    "Team follows ESLint rules with no semicolons",
    "User prefers arrow functions over traditional functions",
]

async def add_memory_sequential(session, memory_text, memory_id):
    """Add a memory via the REST API endpoint."""
    url = f"{BASE_URL}/api/v1/memories"
    
    # REST API payload
    payload = {
        "text": memory_text
    }
    
    try:
        start_time = time.time()
        async with session.post(url, json=payload, timeout=30) as response:
            duration = time.time() - start_time
            
            if response.status in [200, 201]:
                result = await response.json()
                print(f"[{memory_id:2d}] ✓ Success in {duration:.3f}s: {memory_text[:50]}...")
                return True, duration, result
            elif response.status == 422:
                result = await response.json()
                print(f"[{memory_id:2d}] ⚠️  Non-memorable in {duration:.3f}s: {memory_text[:50]}...")
                return True, duration, result  # This is expected behavior
            else:
                text = await response.text()
                print(f"[{memory_id:2d}] ✗ HTTP {response.status} in {duration:.3f}s: {text[:100]}")
                return False, duration, text
                
    except asyncio.TimeoutError:
        duration = time.time() - start_time
        print(f"[{memory_id:2d}] ✗ Timeout after {duration:.3f}s: {memory_text[:50]}...")
        return False, duration, "Timeout"
    except Exception as e:
        duration = time.time() - start_time
        print(f"[{memory_id:2d}] ✗ Error after {duration:.3f}s: {e}")
        return False, duration, str(e)

async def main():
    print(f"🚀 Starting Sequential Memory Test")
    print(f"   Target: {BASE_URL}")
    print(f"   User: {USER_ID}")
    print(f"   Memories: {NUM_MEMORIES} (sequential)")
    print(f"   Start time: {datetime.now().isoformat()}")
    print("-" * 80)
    
    # Process memories one at a time
    async with aiohttp.ClientSession() as session:
        overall_start = time.time()
        
        results = []
        for i, memory_text in enumerate(SAMPLE_MEMORIES[:NUM_MEMORIES]):
            print(f"\n📝 Processing memory {i+1}/{NUM_MEMORIES}")
            result = await add_memory_sequential(session, memory_text, i+1)
            results.append(result)
            
            # Small delay between requests
            await asyncio.sleep(0.5)
        
        overall_duration = time.time() - overall_start
        
        # Analyze results
        total_success = 0
        total_failed = 0
        total_duration_sum = 0
        min_duration = float('inf')
        max_duration = 0
        
        for result in results:
            if isinstance(result, Exception):
                total_failed += 1
                continue
                
            success, duration, response = result
            total_duration_sum += duration
            min_duration = min(min_duration, duration)
            max_duration = max(max_duration, duration)
            
            if success:
                total_success += 1
            else:
                total_failed += 1
        
        # Print summary
        print("\n" + "=" * 80)
        print(f"📊 SEQUENTIAL TEST RESULTS")
        print(f"   Total memories attempted: {NUM_MEMORIES}")
        print(f"   Successful: {total_success}")
        print(f"   Failed: {total_failed}")
        print(f"   Success rate: {(total_success/NUM_MEMORIES)*100:.1f}%")
        print(f"   Overall duration: {overall_duration:.3f}s")
        
        if total_success > 0:
            avg_duration = total_duration_sum / (total_success + total_failed)
            print(f"   Average request time: {avg_duration:.3f}s")
            print(f"   Min request time: {min_duration:.3f}s")
            print(f"   Max request time: {max_duration:.3f}s")
        
        print(f"   End time: {datetime.now().isoformat()}")
        
        if total_failed > 0:
            print(f"\n❌ Some requests failed:")
            print(f"   {total_failed}/{NUM_MEMORIES} memories failed")
        else:
            print(f"\n✅ All requests completed successfully!")
            print(f"   Sequential processing works correctly")

if __name__ == "__main__":
    asyncio.run(main())
