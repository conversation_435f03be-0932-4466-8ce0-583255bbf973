#!/usr/bin/env python3
"""
Demonstration of concurrent operation handling in MemoryClientSingleton.
This script shows how the new concurrent operation features work.
"""

import sys
import os
import time
import threading
from unittest.mock import Mock

# Add the API directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'api'))

def demo_concurrent_operations():
    """Demonstrate concurrent operation handling."""
    print("🚀 Concurrent Operation Handling Demo")
    print("=" * 50)
    
    try:
        # Import the enhanced memory singleton
        from app.utils.memory import MemoryClientSingleton
        
        print("✓ Successfully imported enhanced MemoryClientSingleton")
        
        # Create singleton instance
        singleton = MemoryClientSingleton()
        print("✓ Created singleton instance")
        
        # Create a mock client for demonstration
        mock_client = Mock()
        mock_client.add.return_value = {"id": "demo-id", "status": "success"}
        mock_client.search.return_value = [{"id": "search-result", "content": "demo content"}]
        
        # Set the mock client (this would normally be done through get_client)
        singleton._client = mock_client
        print("✓ Set up mock client for demonstration")
        
        # Show initial queue status
        status = singleton.get_operation_queue_status()
        print(f"\n📊 Initial Queue Status:")
        print(f"   Queue Size: {status['queue_size']}")
        print(f"   Worker Running: {status['worker_running']}")
        print(f"   Worker Alive: {status['worker_alive']}")
        print(f"   Operation Counter: {status['operation_counter']}")
        
        # Demonstrate async memory addition
        print(f"\n🔄 Demonstrating Async Memory Operations:")
        
        # Track results
        results = []
        def result_callback(success, result, operation_id):
            results.append({
                'operation_id': operation_id,
                'success': success,
                'result': result,
                'timestamp': time.time()
            })
            print(f"   ✅ Operation {operation_id} completed: {'Success' if success else 'Failed'}")
        
        # Queue several memory operations
        print(f"\n📝 Queuing memory operations...")
        for i in range(5):
            success, message = singleton.add_memory_async(
                f"Demo memory content {i}",
                {"demo": True, "index": i},
                result_callback
            )
            if success:
                print(f"   ✓ Queued operation {i+1}: {message}")
            else:
                print(f"   ✗ Failed to queue operation {i+1}: {message}")
        
        # Show queue status after queuing
        status = singleton.get_operation_queue_status()
        print(f"\n📊 Queue Status After Queuing:")
        print(f"   Queue Size: {status['queue_size']}")
        print(f"   Worker Running: {status['worker_running']}")
        print(f"   Worker Alive: {status['worker_alive']}")
        print(f"   Operation Counter: {status['operation_counter']}")
        
        # Queue a search operation
        print(f"\n🔍 Queuing search operation...")
        success, message = singleton.search_memory_async(
            "demo query",
            limit=10,
            callback=result_callback
        )
        if success:
            print(f"   ✓ Queued search: {message}")
        else:
            print(f"   ✗ Failed to queue search: {message}")
        
        # Wait for all operations to complete
        print(f"\n⏳ Waiting for operations to complete...")
        if singleton.wait_for_queue_empty(timeout=10.0):
            print("   ✓ All operations completed")
        else:
            print("   ⚠️  Timeout waiting for operations")
        
        # Wait a bit more for callbacks
        time.sleep(0.5)
        
        # Show final results
        print(f"\n📈 Final Results:")
        print(f"   Total Operations Completed: {len(results)}")
        for result in results:
            print(f"   - Operation {result['operation_id']}: {'✅' if result['success'] else '❌'}")
        
        # Show final queue status
        status = singleton.get_operation_queue_status()
        print(f"\n📊 Final Queue Status:")
        print(f"   Queue Size: {status['queue_size']}")
        print(f"   Worker Running: {status['worker_running']}")
        print(f"   Operation Counter: {status['operation_counter']}")
        
        # Demonstrate concurrent access
        print(f"\n🔀 Demonstrating Concurrent Access:")
        instances = []
        
        def get_instance():
            instances.append(MemoryClientSingleton())
        
        # Create multiple threads accessing singleton
        threads = [threading.Thread(target=get_instance) for _ in range(5)]
        
        for thread in threads:
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # Verify all instances are the same
        all_same = all(instance is instances[0] for instance in instances)
        print(f"   ✓ All {len(instances)} instances are identical: {all_same}")
        
        # Clean up
        singleton._stop_worker()
        time.sleep(0.2)
        
        print(f"\n🎉 Demo completed successfully!")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_features():
    """Show the new concurrent operation features."""
    print("\n🌟 New Concurrent Operation Features:")
    print("=" * 50)
    print("✓ Thread-safe operation queuing")
    print("✓ Background worker thread for processing")
    print("✓ Async memory addition (add_memory_async)")
    print("✓ Async memory search (search_memory_async)")
    print("✓ Operation status tracking and callbacks")
    print("✓ Queue status monitoring")
    print("✓ Proper error handling and recovery")
    print("✓ Worker thread lifecycle management")
    print("✓ Concurrent access protection")
    print("=" * 50)

if __name__ == "__main__":
    show_features()
    success = demo_concurrent_operations()
    
    if success:
        print("\n🎯 Task 6: Implement Concurrent Operation Handling - COMPLETED!")
        print("\nThe MemoryClientSingleton now supports:")
        print("• Concurrent memory operations without interference")
        print("• Operation queuing with background processing")
        print("• Async/await patterns for memory operations")
        print("• Operation status tracking and callbacks")
        print("• Thread-safe access to all functionality")
    else:
        print("\n❌ Demo failed - please check the implementation")
    
    sys.exit(0 if success else 1)
