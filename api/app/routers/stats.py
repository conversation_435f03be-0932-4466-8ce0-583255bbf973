from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.database import get_db
from app.models import User, Memory, App, MemoryState
from app.auth.middleware import get_current_user, AuthenticatedUser, DefaultUser
from app.config import USER_ID
from typing import Union


router = APIRouter(prefix="/api/v1/stats", tags=["stats"])

@router.get("")
async def get_profile(
    auth_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Always serve data for the configured MCP user regardless of which Supabase user is logged in
    # This implements the architecture where frontend Supabase auth is for UI protection only
    user = db.query(User).filter(User.user_id == USER_ID).first()
    if not user:
        raise HTTPException(status_code=404, detail=f"MCP user '{USER_ID}' not found in database")
    
    # Get total number of memories
    total_memories = db.query(Memory).filter(Memory.user_id == user.id, Memory.state != MemoryState.deleted.value).count()

    # Get total number of apps
    apps = db.query(App).filter(App.owner == user)
    total_apps = apps.count()

    return {
        "total_memories": total_memories,
        "total_apps": total_apps,
        "apps": apps.all()
    }

