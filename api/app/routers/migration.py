"""
Migration Management API Endpoints
"""
import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel

from app.database.migration import DatabaseMigrator, MigrationConfig, MigrationMode
from app.database.service import db_service
from app.auth.middleware import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/migration", tags=["migration"])


class MigrationRequest(BaseModel):
    validate_data: bool = True
    batch_size: Optional[int] = None


class MigrationModeRequest(BaseModel):
    mode: str


class SyncRequest(BaseModel):
    table: str
    record_id: Optional[str] = None


@router.get("/status")
async def get_migration_status(current_user=Depends(get_current_user)):
    """Get current migration and sync status"""
    try:
        # Get configuration
        config = MigrationConfig()
        
        # Get sync status
        sync_status = db_service.get_sync_status()
        
        # Get health status
        health_status = db_service.health_check()
        
        return {
            "configuration": {
                "migration_mode": config.mode.value,
                "batch_size": config.batch_size,
                "validation_enabled": config.validation_enabled,
                "logging_enabled": config.enable_logging,
                "postgres_available": config.postgres_url is not None
            },
            "sync_status": sync_status,
            "health_status": health_status
        }
        
    except Exception as e:
        logger.error(f"Failed to get migration status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/migrate")
async def start_migration(
    request: MigrationRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user)
):
    """Start database migration process"""
    try:
        config = MigrationConfig()
        
        # Update config if provided
        if request.batch_size:
            import os
            os.environ["MIGRATION_BATCH_SIZE"] = str(request.batch_size)
            config = MigrationConfig()  # Reload with new batch size
        
        # Check if migration is possible
        if not config.postgres_url:
            raise HTTPException(
                status_code=400,
                detail="PostgreSQL URL not configured. Set SUPABASE_DATABASE_URL environment variable."
            )
        
        if config.mode == MigrationMode.SUPABASE_ONLY:
            raise HTTPException(
                status_code=400,
                detail="Cannot migrate when already in supabase_only mode"
            )
        
        # Run migration in background
        def run_migration():
            try:
                migrator = DatabaseMigrator(config)
                success = migrator.run_migration()
                logger.info(f"Migration completed with status: {success}")
            except Exception as e:
                logger.error(f"Background migration failed: {e}")
        
        background_tasks.add_task(run_migration)
        
        return {
            "message": "Migration started in background",
            "config": {
                "batch_size": config.batch_size,
                "validation_enabled": request.validate_data
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to start migration: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/rollback")
async def rollback_migration(
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user)
):
    """Rollback migration by clearing PostgreSQL data"""
    try:
        config = MigrationConfig()
        
        if not config.postgres_url:
            raise HTTPException(
                status_code=400,
                detail="PostgreSQL not configured"
            )
        
        # Run rollback in background
        def run_rollback():
            try:
                migrator = DatabaseMigrator(config)
                migrator.rollback_migration()
                logger.info("Migration rollback completed")
            except Exception as e:
                logger.error(f"Background rollback failed: {e}")
        
        background_tasks.add_task(run_rollback)
        
        return {"message": "Rollback started in background"}
        
    except Exception as e:
        logger.error(f"Failed to start rollback: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/set-mode")
async def set_migration_mode(
    request: MigrationModeRequest,
    current_user=Depends(get_current_user)
):
    """Set migration mode"""
    try:
        # Validate mode
        try:
            mode = MigrationMode(request.mode)
        except ValueError:
            valid_modes = [m.value for m in MigrationMode]
            raise HTTPException(
                status_code=400,
                detail=f"Invalid mode '{request.mode}'. Valid modes: {valid_modes}"
            )
        
        # Update environment variable
        import os
        os.environ["MIGRATION_MODE"] = request.mode
        
        # Reinitialize dual-write manager with new mode
        from app.database.dual_write import dual_write_manager
        dual_write_manager.__init__()
        
        return {
            "message": f"Migration mode set to {request.mode}",
            "mode": request.mode,
            "effective_immediately": True
        }
        
    except Exception as e:
        logger.error(f"Failed to set migration mode: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sync")
async def manual_sync(
    request: SyncRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user)
):
    """Manually sync data between databases"""
    try:
        from app.models import User, App, Memory, Category
        
        model_mapping = {
            "users": User,
            "apps": App,
            "memories": Memory,
            "categories": Category
        }
        
        if request.table not in model_mapping:
            raise HTTPException(
                status_code=400,
                detail=f"Unknown table: {request.table}"
            )
        
        model_class = model_mapping[request.table]
        
        def run_sync():
            try:
                from app.database.dual_write import dual_write_manager
                
                if request.record_id:
                    # Sync specific record
                    success = dual_write_manager.sync_record(model_class, request.record_id)
                    logger.info(f"Sync record {request.record_id}: {'success' if success else 'failed'}")
                else:
                    # Sync all records in table
                    with dual_write_manager.get_session("sqlite") as session:
                        records = session.query(model_class).all()
                        total = len(records)
                        synced = 0
                        
                        for record in records:
                            if dual_write_manager.sync_record(model_class, record.id):
                                synced += 1
                        
                        logger.info(f"Synced {synced}/{total} records for {request.table}")
                        
            except Exception as e:
                logger.error(f"Background sync failed: {e}")
        
        background_tasks.add_task(run_sync)
        
        return {
            "message": f"Sync started for {request.table}",
            "table": request.table,
            "record_id": request.record_id
        }
        
    except Exception as e:
        logger.error(f"Failed to start sync: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate")
async def validate_data(current_user=Depends(get_current_user)):
    """Validate data integrity between databases"""
    try:
        config = MigrationConfig()
        migrator = DatabaseMigrator(config)
        
        if not migrator.validator:
            raise HTTPException(
                status_code=400,
                detail="Validator not available - PostgreSQL connection required"
            )
        
        validation_results = {}
        validation_tables = [
            "users", "apps", "configs", "memories", "categories",
            "access_controls", "archive_policies", "memory_status_history", "memory_access_logs"
        ]
        
        overall_valid = True
        
        for table_name in validation_tables:
            # Count validation
            count_valid = migrator.validator.validate_table_counts(table_name)
            
            # UUID validation for relevant tables
            uuid_valid = True
            if table_name in ["users", "apps", "memories", "categories"]:
                uuid_columns = ["id"]
                if table_name == "apps":
                    uuid_columns.append("owner_id")
                elif table_name == "memories":
                    uuid_columns.extend(["user_id", "app_id"])
                
                uuid_valid = migrator.validator.validate_uuid_integrity(table_name, uuid_columns)
            
            table_valid = count_valid and uuid_valid
            overall_valid = overall_valid and table_valid
            
            validation_results[table_name] = {
                "count_valid": count_valid,
                "uuid_valid": uuid_valid,
                "overall_valid": table_valid
            }
        
        return {
            "overall_valid": overall_valid,
            "table_results": validation_results,
            "validation_timestamp": db_service.dual_write.health.last_postgres_check.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Data validation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/backup")
async def create_backup(current_user=Depends(get_current_user)):
    """Create backup of SQLite database"""
    try:
        config = MigrationConfig()
        migrator = DatabaseMigrator(config)
        backup_path = migrator.backup_sqlite_database()
        
        return {
            "message": "Backup created successfully",
            "backup_path": backup_path,
            "timestamp": migrator.status.start_time.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Backup creation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def check_health():
    """Check database health (no auth required for health checks)"""
    try:
        health_status = db_service.health_check()
        
        # Determine overall health
        sqlite_healthy = health_status.get("sqlite", {}).get("healthy", False)
        postgres_info = health_status.get("postgres")
        postgres_healthy = postgres_info.get("healthy", False) if postgres_info else None
        
        overall_healthy = sqlite_healthy and (postgres_healthy is None or postgres_healthy)
        
        return {
            "overall_healthy": overall_healthy,
            "sqlite_healthy": sqlite_healthy,
            "postgres_healthy": postgres_healthy,
            "postgres_configured": postgres_info is not None,
            "details": health_status
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config")
async def get_migration_config(current_user=Depends(get_current_user)):
    """Get current migration configuration"""
    try:
        config = MigrationConfig()
        
        return {
            "migration_mode": config.mode.value,
            "batch_size": config.batch_size,
            "validation_enabled": config.validation_enabled,
            "logging_enabled": config.enable_logging,
            "sqlite_url": config.sqlite_url,
            "postgres_configured": config.postgres_url is not None,
            "available_modes": [mode.value for mode in MigrationMode]
        }
        
    except Exception as e:
        logger.error(f"Failed to get migration config: {e}")
        raise HTTPException(status_code=500, detail=str(e))