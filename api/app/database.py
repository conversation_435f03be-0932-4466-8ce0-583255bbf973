import os
from dotenv import load_dotenv

# Import base components
from app.database.base import Base, engine as base_engine, SessionLocal as base_session, get_db as base_get_db

# load .env file (make sure you have DATABASE_URL set)
load_dotenv()

# Check if dual-write system is available
MIGRATION_MODE = os.getenv("MIGRATION_MODE", "sqlite_only")
USE_DUAL_WRITE = MIGRATION_MODE != "sqlite_only"

if USE_DUAL_WRITE:
    try:
        from app.database import get_dual_write_manager
        dual_write_manager = get_dual_write_manager()
        
        # Use dual-write manager for database operations
        def get_db():
            with dual_write_manager.get_session("primary") as db:
                yield db
                
        # For backward compatibility, expose the engines
        engine = dual_write_manager.sqlite_engine
        SessionLocal = dual_write_manager.sqlite_session
        
    except ImportError:
        # Fallback to original implementation if dual-write not available
        USE_DUAL_WRITE = False

if not USE_DUAL_WRITE:
    # Use base implementation
    engine = base_engine
    SessionLocal = base_session
    get_db = base_get_db
