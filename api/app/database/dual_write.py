"""
Dual-Write Database Abstraction Layer
Supports writing to both SQLite and Supabase PostgreSQL during migration
"""
import os
import logging
import json
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime, timezone
from contextlib import contextmanager
from enum import Enum

from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from dotenv import load_dotenv

from app.database.migration import MigrationMode, MigrationConfig

logger = logging.getLogger(__name__)
load_dotenv()


class WriteResult:
    """Result of a write operation"""
    
    def __init__(self, success: bool, error: Optional[str] = None, data: Optional[Any] = None):
        self.success = success
        self.error = error
        self.data = data
        self.timestamp = datetime.now(timezone.utc)


class ConnectionHealth:
    """Track database connection health"""
    
    def __init__(self):
        self.sqlite_healthy = True
        self.postgres_healthy = True
        self.last_sqlite_check = datetime.now(timezone.utc)
        self.last_postgres_check = datetime.now(timezone.utc)
        self.sqlite_error_count = 0
        self.postgres_error_count = 0
        
    def mark_sqlite_error(self):
        self.sqlite_error_count += 1
        if self.sqlite_error_count >= 3:
            self.sqlite_healthy = False
            
    def mark_postgres_error(self):
        self.postgres_error_count += 1
        if self.postgres_error_count >= 3:
            self.postgres_healthy = False
            
    def reset_sqlite_health(self):
        self.sqlite_healthy = True
        self.sqlite_error_count = 0
        self.last_sqlite_check = datetime.now(timezone.utc)
        
    def reset_postgres_health(self):
        self.postgres_healthy = True
        self.postgres_error_count = 0
        self.last_postgres_check = datetime.now(timezone.utc)


class DualWriteManager:
    """Manages dual-write operations to SQLite and PostgreSQL"""
    
    def __init__(self):
        self.config = MigrationConfig()
        self.health = ConnectionHealth()
        
        # Setup SQLite
        if self.config.sqlite_url.startswith('sqlite'):
            self.sqlite_engine = create_engine(
                self.config.sqlite_url,
                connect_args={"check_same_thread": False}
            )
        else:
            # If DATABASE_URL is PostgreSQL, use default SQLite path
            sqlite_url = "sqlite:///./openmemory.db"
            self.sqlite_engine = create_engine(
                sqlite_url,
                connect_args={"check_same_thread": False}
            )
        self.sqlite_session = sessionmaker(bind=self.sqlite_engine)
        
        # Setup PostgreSQL (if available)
        self.postgres_engine = None
        self.postgres_session = None
        
        if self.config.postgres_url and self.config.mode != MigrationMode.SQLITE_ONLY:
            try:
                self.postgres_engine = create_engine(self.config.postgres_url)
                self.postgres_session = sessionmaker(bind=self.postgres_engine)
                
                # Test connection
                with self.postgres_engine.connect() as conn:
                    from sqlalchemy import text
                    conn.execute(text("SELECT 1"))
                logger.info("PostgreSQL connection established")
                
            except Exception as e:
                logger.error(f"Failed to connect to PostgreSQL: {e}")
                if self.config.mode == MigrationMode.SUPABASE_ONLY:
                    raise
                self.health.mark_postgres_error()
        
        # Setup write strategy based on mode
        self._setup_write_strategy()
        
    def _setup_write_strategy(self):
        """Configure write strategy based on migration mode"""
        self.write_to_sqlite = self.config.mode in [MigrationMode.SQLITE_ONLY, MigrationMode.DUAL_WRITE]
        self.write_to_postgres = self.config.mode in [MigrationMode.DUAL_WRITE, MigrationMode.SUPABASE_ONLY]
        
        logger.info(f"Write strategy: SQLite={self.write_to_sqlite}, PostgreSQL={self.write_to_postgres}")
    
    def check_connection_health(self) -> Dict[str, Any]:
        """Check health of both database connections"""
        now = datetime.now(timezone.utc)
        
        # Check SQLite health
        try:
            with self.sqlite_engine.connect() as conn:
                from sqlalchemy import text
                conn.execute(text("SELECT 1"))
            self.health.reset_sqlite_health()
        except Exception as e:
            logger.error(f"SQLite health check failed: {e}")
            self.health.mark_sqlite_error()
        
        # Check PostgreSQL health
        if self.postgres_engine:
            try:
                with self.postgres_engine.connect() as conn:
                    from sqlalchemy import text
                    conn.execute(text("SELECT 1"))
                self.health.reset_postgres_health()
            except Exception as e:
                logger.error(f"PostgreSQL health check failed: {e}")
                self.health.mark_postgres_error()
        
        return {
            "sqlite": {
                "healthy": self.health.sqlite_healthy,
                "error_count": self.health.sqlite_error_count,
                "last_check": self.health.last_sqlite_check.isoformat()
            },
            "postgres": {
                "healthy": self.health.postgres_healthy,
                "error_count": self.health.postgres_error_count,
                "last_check": self.health.last_postgres_check.isoformat()
            } if self.postgres_engine else None
        }
    
    @contextmanager
    def get_session(self, database: str = "primary"):
        """Get database session with automatic cleanup"""
        if database == "sqlite" or (database == "primary" and self.config.mode == MigrationMode.SQLITE_ONLY):
            session = self.sqlite_session()
            try:
                yield session
            finally:
                session.close()
                
        elif database == "postgres" or (database == "primary" and self.config.mode == MigrationMode.SUPABASE_ONLY):
            if not self.postgres_session:
                raise ValueError("PostgreSQL not available")
            session = self.postgres_session()
            try:
                yield session
            finally:
                session.close()
                
        else:  # dual_write mode
            # Return SQLite as primary for dual-write
            session = self.sqlite_session()
            try:
                yield session
            finally:
                session.close()
    
    def _write_to_sqlite(self, operation_func, *args, **kwargs) -> WriteResult:
        """Perform write operation to SQLite"""
        if not self.write_to_sqlite or not self.health.sqlite_healthy:
            return WriteResult(False, "SQLite not available or unhealthy")
        
        try:
            with self.sqlite_session() as session:
                result = operation_func(session, *args, **kwargs)
                session.commit()
                return WriteResult(True, data=result)
                
        except Exception as e:
            logger.error(f"SQLite write failed: {e}")
            self.health.mark_sqlite_error()
            return WriteResult(False, str(e))
    
    def _write_to_postgres(self, operation_func, *args, **kwargs) -> WriteResult:
        """Perform write operation to PostgreSQL"""
        if not self.write_to_postgres or not self.postgres_session or not self.health.postgres_healthy:
            return WriteResult(False, "PostgreSQL not available or unhealthy")
        
        try:
            with self.postgres_session() as session:
                result = operation_func(session, *args, **kwargs)
                session.commit()
                return WriteResult(True, data=result)
                
        except Exception as e:
            logger.error(f"PostgreSQL write failed: {e}")
            self.health.mark_postgres_error()
            return WriteResult(False, str(e))
    
    def dual_write(self, operation_func, *args, **kwargs) -> Tuple[WriteResult, Optional[WriteResult]]:
        """
        Perform dual write operation
        Returns: (primary_result, secondary_result)
        """
        primary_result = None
        secondary_result = None
        
        if self.config.mode == MigrationMode.SQLITE_ONLY:
            primary_result = self._write_to_sqlite(operation_func, *args, **kwargs)
            
        elif self.config.mode == MigrationMode.SUPABASE_ONLY:
            primary_result = self._write_to_postgres(operation_func, *args, **kwargs)
            
        elif self.config.mode == MigrationMode.DUAL_WRITE:
            # SQLite is primary, PostgreSQL is secondary
            primary_result = self._write_to_sqlite(operation_func, *args, **kwargs)
            
            if primary_result.success:
                # Only write to secondary if primary succeeds
                secondary_result = self._write_to_postgres(operation_func, *args, **kwargs)
                
                if not secondary_result.success:
                    logger.warning(f"Secondary write failed: {secondary_result.error}")
                    # Consider implementing retry logic here
            else:
                logger.error(f"Primary write failed: {primary_result.error}")
        
        return primary_result, secondary_result
    
    def create_record(self, model_class, **kwargs) -> Tuple[WriteResult, Optional[WriteResult]]:
        """Create a new record using dual-write"""
        
        def _create_operation(session: Session, model_class, **kwargs):
            instance = model_class(**kwargs)
            session.add(instance)
            session.flush()  # Get the ID
            return instance
        
        return self.dual_write(_create_operation, model_class, **kwargs)
    
    def update_record(self, model_class, record_id: Any, **kwargs) -> Tuple[WriteResult, Optional[WriteResult]]:
        """Update an existing record using dual-write"""
        
        def _update_operation(session: Session, model_class, record_id: Any, **kwargs):
            instance = session.query(model_class).filter(model_class.id == record_id).first()
            if not instance:
                raise ValueError(f"Record not found: {record_id}")
            
            for key, value in kwargs.items():
                if hasattr(instance, key):
                    setattr(instance, key, value)
            
            # Update timestamp if available
            if hasattr(instance, 'updated_at'):
                instance.updated_at = datetime.now(timezone.utc)
            
            session.flush()
            return instance
        
        return self.dual_write(_update_operation, model_class, record_id, **kwargs)
    
    def delete_record(self, model_class, record_id: Any, soft_delete: bool = True) -> Tuple[WriteResult, Optional[WriteResult]]:
        """Delete a record using dual-write"""
        
        def _delete_operation(session: Session, model_class, record_id: Any, soft_delete: bool):
            instance = session.query(model_class).filter(model_class.id == record_id).first()
            if not instance:
                raise ValueError(f"Record not found: {record_id}")
            
            if soft_delete and hasattr(instance, 'deleted_at'):
                instance.deleted_at = datetime.now(timezone.utc)
                if hasattr(instance, 'state'):
                    instance.state = "deleted"
            else:
                session.delete(instance)
            
            session.flush()
            return instance
        
        return self.dual_write(_delete_operation, model_class, record_id, soft_delete)
    
    def execute_raw_sql(self, sql: str, params: Optional[Dict] = None) -> Tuple[WriteResult, Optional[WriteResult]]:
        """Execute raw SQL using dual-write"""
        
        def _raw_sql_operation(session: Session, sql: str, params: Optional[Dict] = None):
            result = session.execute(sql, params or {})
            return result
        
        return self.dual_write(_raw_sql_operation, sql, params)
    
    def sync_record(self, model_class, record_id: Any) -> bool:
        """Manually sync a record from SQLite to PostgreSQL"""
        if not self.postgres_session:
            return False
        
        try:
            # Get record from SQLite
            with self.sqlite_session() as sqlite_session:
                record = sqlite_session.query(model_class).filter(model_class.id == record_id).first()
                if not record:
                    logger.error(f"Record not found in SQLite: {record_id}")
                    return False
            
            # Write to PostgreSQL
            with self.postgres_session() as postgres_session:
                # Check if record exists
                existing = postgres_session.query(model_class).filter(model_class.id == record_id).first()
                
                if existing:
                    # Update existing record
                    for column in model_class.__table__.columns:
                        column_name = column.name
                        if hasattr(record, column_name):
                            setattr(existing, column_name, getattr(record, column_name))
                else:
                    # Create new record
                    record_dict = {}
                    for column in model_class.__table__.columns:
                        column_name = column.name
                        if hasattr(record, column_name):
                            record_dict[column_name] = getattr(record, column_name)
                    
                    new_record = model_class(**record_dict)
                    postgres_session.add(new_record)
                
                postgres_session.commit()
                logger.info(f"Successfully synced record {record_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to sync record {record_id}: {e}")
            return False
    
    def get_sync_status(self) -> Dict[str, Any]:
        """Get synchronization status between databases"""
        if not self.postgres_session:
            return {"error": "PostgreSQL not available"}
        
        status = {}
        
        try:
            from app.models import User, App, Memory, Category
            
            for model_class in [User, App, Memory, Category]:
                table_name = model_class.__tablename__
                
                with self.sqlite_session() as sqlite_session:
                    from sqlalchemy import text
                    sqlite_count = sqlite_session.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
                
                with self.postgres_session() as postgres_session:
                    postgres_count = postgres_session.query(model_class).count()
                
                status[table_name] = {
                    "sqlite_count": sqlite_count,
                    "postgres_count": postgres_count,
                    "in_sync": sqlite_count == postgres_count,
                    "difference": sqlite_count - postgres_count
                }
        
        except Exception as e:
            status["error"] = str(e)
        
        return status


# Global instance
dual_write_manager = DualWriteManager()