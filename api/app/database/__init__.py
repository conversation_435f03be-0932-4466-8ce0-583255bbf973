"""
Database package initialization
Provides unified database access with dual-write support
"""
from .base import Base, engine, SessionLocal, get_db
from .migration import MigrationConfig, MigrationMode, DatabaseMigrator
from .cli import main as cli_main

# Import dual_write_manager lazily to avoid circular imports
def get_dual_write_manager():
    from .dual_write import dual_write_manager
    return dual_write_manager

__all__ = [
    'Base',
    'engine',
    'SessionLocal',
    'get_db',
    'get_dual_write_manager',
    'MigrationConfig',
    'MigrationMode',
    'DatabaseMigrator',
    'cli_main'
]