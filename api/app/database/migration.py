"""
Database Migration System for SQLite to Supabase PostgreSQL transition
"""
import os
import json
import logging
import asyncio
import sqlite3
from typing import Dict, List, Any, Optional, Tuple, Union
from enum import Enum
from datetime import datetime, timezone
from uuid import UUID
import uuid

import psycopg2
from psycopg2.extras import Dict<PERSON>ursor, execute_batch
from sqlalchemy import create_engine, MetaData, Table, select, and_
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.dialects import postgresql, sqlite
from dotenv import load_dotenv

from app.models import (
    User, App, Config, Memory, Category, AccessControl, 
    ArchivePolicy, MemoryStatusHistory, MemoryAccessLog, memory_categories
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()


class MigrationMode(Enum):
    SQLITE_ONLY = "sqlite_only"
    DUAL_WRITE = "dual_write"
    SUPABASE_ONLY = "supabase_only"


class MigrationConfig:
    """Configuration for migration system"""
    
    def __init__(self):
        self.mode = MigrationMode(os.getenv("MIGRATION_MODE", "sqlite_only"))
        self.batch_size = int(os.getenv("MIGRATION_BATCH_SIZE", "100"))
        self.enable_logging = os.getenv("ENABLE_MIGRATION_LOGGING", "true").lower() == "true"
        self.validation_enabled = os.getenv("MIGRATION_VALIDATION_ENABLED", "true").lower() == "true"
        
        # Handle SQLite URL - if DATABASE_URL is PostgreSQL, use default SQLite path
        database_url = os.getenv("DATABASE_URL", "sqlite:///./openmemory.db")
        if database_url.startswith('sqlite'):
            self.sqlite_url = database_url
        else:
            self.sqlite_url = "sqlite:///./openmemory.db"
            
        # Use SUPABASE_DATABASE_URL if available, otherwise fall back to DATABASE_URL if it's PostgreSQL
        self.postgres_url = os.getenv("SUPABASE_DATABASE_URL")
        if not self.postgres_url and database_url.startswith('postgresql'):
            self.postgres_url = database_url
        
        if not self.postgres_url and self.mode != MigrationMode.SQLITE_ONLY:
            raise ValueError("SUPABASE_DATABASE_URL or PostgreSQL DATABASE_URL required for dual-write or supabase-only mode")


class MigrationStatus:
    """Track migration progress and status"""
    
    def __init__(self):
        self.start_time = datetime.now(timezone.utc)
        self.total_records = 0
        self.migrated_records = 0
        self.failed_records = 0
        self.current_table = ""
        self.errors: List[Dict[str, Any]] = []
        self.completed_tables: List[str] = []
        
    def log_error(self, table: str, record_id: Any, error: str):
        """Log migration error"""
        self.errors.append({
            "table": table,
            "record_id": str(record_id),
            "error": error,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        self.failed_records += 1
        
    def progress_report(self) -> Dict[str, Any]:
        """Generate progress report"""
        elapsed = datetime.now(timezone.utc) - self.start_time
        return {
            "start_time": self.start_time.isoformat(),
            "elapsed_seconds": elapsed.total_seconds(),
            "current_table": self.current_table,
            "total_records": self.total_records,
            "migrated_records": self.migrated_records,
            "failed_records": self.failed_records,
            "completed_tables": self.completed_tables,
            "success_rate": (self.migrated_records / max(1, self.total_records)) * 100,
            "errors": self.errors[-10:]  # Last 10 errors
        }


class DataValidator:
    """Validate data integrity between SQLite and PostgreSQL"""
    
    def __init__(self, sqlite_engine, postgres_engine):
        self.sqlite_engine = sqlite_engine
        self.postgres_engine = postgres_engine
        
    def validate_table_counts(self, table_name: str) -> bool:
        """Compare record counts between databases"""
        try:
            # SQLite count
            with self.sqlite_engine.connect() as conn:
                from sqlalchemy import text
                sqlite_count = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
            
            # PostgreSQL count
            with self.postgres_engine.connect() as conn:
                from sqlalchemy import text
                pg_count = conn.execute(text(f"SELECT COUNT(*) FROM memory_master.{table_name}")).scalar()
            
            if sqlite_count != pg_count:
                logger.error(f"Count mismatch for {table_name}: SQLite={sqlite_count}, PostgreSQL={pg_count}")
                return False
                
            logger.info(f"Count validation passed for {table_name}: {sqlite_count} records")
            return True
            
        except Exception as e:
            logger.error(f"Count validation failed for {table_name}: {e}")
            return False
    
    def validate_foreign_keys(self, table_name: str, foreign_key_checks: List[Tuple[str, str, str]]) -> bool:
        """Validate foreign key relationships"""
        try:
            for fk_column, ref_table, ref_column in foreign_key_checks:
                with self.postgres_engine.connect() as conn:
                    from sqlalchemy import text
                    # Check for orphaned records
                    query = f"""
                    SELECT COUNT(*) FROM memory_master.{table_name} t1
                    LEFT JOIN memory_master.{ref_table} t2 ON t1.{fk_column} = t2.{ref_column}
                    WHERE t1.{fk_column} IS NOT NULL AND t2.{ref_column} IS NULL
                    """
                    orphaned = conn.execute(text(query)).scalar()
                    
                    if orphaned > 0:
                        logger.error(f"Found {orphaned} orphaned records in {table_name}.{fk_column}")
                        return False
            
            logger.info(f"Foreign key validation passed for {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"Foreign key validation failed for {table_name}: {e}")
            return False
    
    def validate_uuid_integrity(self, table_name: str, uuid_columns: List[str]) -> bool:
        """Validate UUID format and uniqueness"""
        try:
            with self.postgres_engine.connect() as conn:
                from sqlalchemy import text
                for column in uuid_columns:
                    # Check UUID format
                    query = f"""
                    SELECT COUNT(*) FROM memory_master.{table_name}
                    WHERE {column} IS NOT NULL
                    AND {column}::text !~ '^[0-9a-f]{{8}}-[0-9a-f]{{4}}-[0-9a-f]{{4}}-[0-9a-f]{{4}}-[0-9a-f]{{12}}$'
                    """
                    invalid_uuids = conn.execute(text(query)).scalar()
                    
                    if invalid_uuids > 0:
                        logger.error(f"Found {invalid_uuids} invalid UUIDs in {table_name}.{column}")
                        return False
            
            logger.info(f"UUID validation passed for {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"UUID validation failed for {table_name}: {e}")
            return False


class DatabaseMigrator:
    """Main migration engine for SQLite to PostgreSQL"""
    
    def __init__(self, config: MigrationConfig):
        self.config = config
        self.status = MigrationStatus()
        
        # Setup engines
        self.sqlite_engine = create_engine(config.sqlite_url)
        self.postgres_engine = create_engine(config.postgres_url) if config.postgres_url else None
        
        # Setup sessions
        self.sqlite_session = sessionmaker(bind=self.sqlite_engine)
        self.postgres_session = sessionmaker(bind=self.postgres_engine) if self.postgres_engine else None
        
        # Setup validator
        self.validator = DataValidator(self.sqlite_engine, self.postgres_engine) if self.postgres_engine else None
        
        # Migration order (respect foreign key dependencies)
        self.migration_order = [
            "users",
            "apps",
            "configs",
            "categories",
            "memories",
            "access_controls",
            "archive_policies",
            "memory_status_history",
            "memory_access_logs",
            "memory_categories"  # Association table last
        ]
        
    def backup_sqlite_database(self) -> str:
        """Create backup of SQLite database"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"openmemory_backup_{timestamp}.db"
        
        try:
            import shutil
            # Extract the actual file path from SQLite URL
            if self.config.sqlite_url.startswith('sqlite:///'):
                source_path = self.config.sqlite_url.replace('sqlite:///', '')
                if source_path.startswith('./'):
                    source_path = source_path[2:]  # Remove './' prefix
            else:
                source_path = "openmemory.db"
            
            shutil.copy2(source_path, backup_path)
            logger.info(f"SQLite backup created: {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            raise
    
    def convert_sqlite_to_postgres_value(self, value: Any, column_type: str) -> Any:
        """Convert SQLite values to PostgreSQL compatible format"""
        if value is None:
            return None
            
        # Handle JSON fields
        if column_type.lower() in ['json', 'jsonb']:
            if isinstance(value, str):
                try:
                    return json.loads(value)
                except json.JSONDecodeError:
                    return value
            return value
        
        # Handle UUID fields
        if column_type.lower() == 'uuid':
            if isinstance(value, str):
                try:
                    return UUID(value)
                except ValueError:
                    return uuid.uuid4()
            return value
            
        # Handle datetime fields
        if column_type.lower() in ['datetime', 'timestamp']:
            if isinstance(value, str):
                try:
                    # Parse common datetime formats
                    for fmt in ["%Y-%m-%d %H:%M:%S.%f", "%Y-%m-%d %H:%M:%S"]:
                        try:
                            dt = datetime.strptime(value, fmt)
                            return dt.replace(tzinfo=timezone.utc)
                        except ValueError:
                            continue
                    return datetime.now(timezone.utc)
                except:
                    return datetime.now(timezone.utc)
            return value
            
        return value
    
    def migrate_table_batch(self, table_name: str, records: List[Dict], model_class) -> int:
        """Migrate a batch of records for a specific table"""
        if not self.postgres_session:
            return 0
            
        migrated_count = 0
        
        with self.postgres_session() as session:
            try:
                for record in records:
                    try:
                        # Convert values to appropriate types
                        converted_record = {}
                        for key, value in record.items():
                            if hasattr(model_class, key):
                                column = getattr(model_class.__table__.c, key, None)
                                if column is not None:
                                    column_type = str(column.type)
                                    converted_record[key] = self.convert_sqlite_to_postgres_value(value, column_type)
                                else:
                                    converted_record[key] = value
                            else:
                                converted_record[key] = value
                        
                        # Handle special cases
                        if table_name == "users" and "supabase_user_id" not in converted_record:
                            converted_record["supabase_user_id"] = None
                        
                        if table_name == "memories" and "state" in converted_record:
                            # Ensure state is valid
                            valid_states = ["active", "paused", "archived", "deleted"]
                            if converted_record["state"] not in valid_states:
                                converted_record["state"] = "active"
                        
                        # Create model instance
                        instance = model_class(**converted_record)
                        session.add(instance)
                        migrated_count += 1
                        
                    except Exception as e:
                        self.status.log_error(table_name, record.get('id', 'unknown'), str(e))
                        logger.error(f"Failed to migrate record {record.get('id')}: {e}")
                        continue
                
                session.commit()
                logger.info(f"Migrated batch of {migrated_count} records for {table_name}")
                
            except Exception as e:
                session.rollback()
                logger.error(f"Batch migration failed for {table_name}: {e}")
                raise
                
        return migrated_count
    
    def migrate_table(self, table_name: str) -> bool:
        """Migrate all data for a specific table"""
        self.status.current_table = table_name
        logger.info(f"Starting migration for table: {table_name}")
        
        # Map table names to model classes
        model_mapping = {
            "users": User,
            "apps": App,
            "configs": Config,
            "memories": Memory,
            "categories": Category,
            "access_controls": AccessControl,
            "archive_policies": ArchivePolicy,
            "memory_status_history": MemoryStatusHistory,
            "memory_access_logs": MemoryAccessLog
        }
        
        model_class = model_mapping.get(table_name)
        if not model_class:
            logger.error(f"No model class found for table {table_name}")
            return False
        
        try:
            # Get total count
            with self.sqlite_engine.connect() as conn:
                from sqlalchemy import text
                total_count = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
                self.status.total_records += total_count
                logger.info(f"Found {total_count} records in {table_name}")
            
            # Handle memory_categories association table separately
            if table_name == "memory_categories":
                return self.migrate_memory_categories()
            
            # Migrate in batches
            offset = 0
            while offset < total_count:
                with self.sqlite_engine.connect() as conn:
                    from sqlalchemy import text
                    result = conn.execute(text(f"""
                        SELECT * FROM {table_name}
                        LIMIT {self.config.batch_size} OFFSET {offset}
                    """))
                    
                    records = [dict(row._mapping) for row in result]
                    if not records:
                        break
                    
                    migrated = self.migrate_table_batch(table_name, records, model_class)
                    self.status.migrated_records += migrated
                    offset += len(records)
                    
                    logger.info(f"Progress: {offset}/{total_count} records processed for {table_name}")
            
            self.status.completed_tables.append(table_name)
            logger.info(f"Completed migration for {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"Migration failed for {table_name}: {e}")
            return False
    
    def migrate_memory_categories(self) -> bool:
        """Special migration for memory_categories association table"""
        if not self.postgres_session:
            return False
            
        try:
            with self.sqlite_engine.connect() as conn:
                from sqlalchemy import text
                result = conn.execute(text("SELECT * FROM memory_categories"))
                records = [dict(row._mapping) for row in result]
            
            with self.postgres_session() as session:
                for record in records:
                    try:
                        # Use raw SQL for association table with text() and parameters
                        from sqlalchemy import text
                        session.execute(text("""
                            INSERT INTO memory_master.memory_categories (memory_id, category_id)
                            VALUES (:memory_id, :category_id)
                            ON CONFLICT DO NOTHING
                        """), {
                            'memory_id': record['memory_id'],
                            'category_id': record['category_id']
                        })
                    except Exception as e:
                        self.status.log_error("memory_categories", f"{record['memory_id']}-{record['category_id']}", str(e))
                        continue
                
                session.commit()
                logger.info(f"Migrated {len(records)} memory_categories associations")
                
            return True
            
        except Exception as e:
            logger.error(f"Memory categories migration failed: {e}")
            return False
    
    def run_migration(self) -> bool:
        """Execute full migration"""
        logger.info("Starting database migration from SQLite to PostgreSQL")
        
        # Create backup
        backup_path = self.backup_sqlite_database()
        
        try:
            # Migrate tables in dependency order
            for table_name in self.migration_order:
                success = self.migrate_table(table_name)
                if not success:
                    logger.error(f"Migration failed at table {table_name}")
                    return False
            
            # Run validation if enabled
            if self.config.validation_enabled and self.validator:
                logger.info("Running data validation...")
                
                for table_name in self.migration_order:
                    if table_name == "memory_categories":
                        continue  # Skip association table validation
                        
                    if not self.validator.validate_table_counts(table_name):
                        logger.error(f"Validation failed for {table_name}")
                        return False
            
            logger.info("Migration completed successfully!")
            logger.info(f"Final status: {json.dumps(self.status.progress_report(), indent=2)}")
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return False
    
    def rollback_migration(self):
        """Rollback migration by clearing PostgreSQL data"""
        if not self.postgres_session:
            logger.error("No PostgreSQL connection available for rollback")
            return
            
        try:
            with self.postgres_session() as session:
                # Disable foreign key checks temporarily
                session.execute("SET session_replication_role = replica;")
                
                # Clear tables in reverse dependency order
                for table_name in reversed(self.migration_order):
                    if table_name == "memory_categories":
                        session.execute("DELETE FROM memory_master.memory_categories")
                    else:
                        session.execute(f"DELETE FROM memory_master.{table_name}")
                
                # Re-enable foreign key checks
                session.execute("SET session_replication_role = DEFAULT;")
                session.commit()
                
            logger.info("Migration rollback completed")
            
        except Exception as e:
            logger.error(f"Rollback failed: {e}")