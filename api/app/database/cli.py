"""
CLI Tools for Database Migration Management
"""
import os
import sys
import json
import asyncio
import argparse
from datetime import datetime
from typing import Dict, Any

from app.database.migration import DatabaseMigrator, MigrationConfig, MigrationMode
from app.database.dual_write import dual_write_manager


def print_status(message: str, status: str = "INFO"):
    """Print formatted status message"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {status}: {message}")


def print_json(data: Dict[str, Any]):
    """Print JSON data with formatting"""
    print(json.dumps(data, indent=2, default=str))


def migrate_command(args):
    """Execute database migration"""
    print_status("Starting database migration...")
    
    try:
        config = MigrationConfig()
        migrator = DatabaseMigrator(config)
        
        print_status(f"Migration mode: {config.mode.value}")
        print_status(f"Batch size: {config.batch_size}")
        print_status(f"Validation enabled: {config.validation_enabled}")
        
        if args.dry_run:
            print_status("DRY RUN MODE - No actual migration will be performed")
            return
        
        success = migrator.run_migration()
        
        if success:
            print_status("Migration completed successfully!", "SUCCESS")
            print_json(migrator.status.progress_report())
        else:
            print_status("Migration failed!", "ERROR")
            print_json(migrator.status.progress_report())
            sys.exit(1)
            
    except Exception as e:
        print_status(f"Migration failed with exception: {e}", "ERROR")
        sys.exit(1)


def rollback_command(args):
    """Rollback migration"""
    print_status("Starting migration rollback...")
    
    if not args.confirm:
        response = input("Are you sure you want to rollback the migration? This will delete all PostgreSQL data. (yes/no): ")
        if response.lower() != 'yes':
            print_status("Rollback cancelled")
            return
    
    try:
        config = MigrationConfig()
        migrator = DatabaseMigrator(config)
        migrator.rollback_migration()
        print_status("Rollback completed successfully!", "SUCCESS")
        
    except Exception as e:
        print_status(f"Rollback failed: {e}", "ERROR")
        sys.exit(1)


def status_command(args):
    """Show migration and sync status"""
    print_status("Checking migration status...")
    
    try:
        # Connection health
        health = dual_write_manager.check_connection_health()
        print_status("Database Connection Health:")
        print_json(health)
        
        # Sync status
        sync_status = dual_write_manager.get_sync_status()
        print_status("Database Synchronization Status:")
        print_json(sync_status)
        
        # Current configuration
        config = MigrationConfig()
        config_status = {
            "migration_mode": config.mode.value,
            "batch_size": config.batch_size,
            "validation_enabled": config.validation_enabled,
            "logging_enabled": config.enable_logging,
            "sqlite_url": config.sqlite_url,
            "postgres_available": config.postgres_url is not None
        }
        print_status("Current Configuration:")
        print_json(config_status)
        
    except Exception as e:
        print_status(f"Status check failed: {e}", "ERROR")
        sys.exit(1)


def validate_command(args):
    """Validate data integrity between databases"""
    print_status("Starting data validation...")
    
    try:
        config = MigrationConfig()
        migrator = DatabaseMigrator(config)
        
        if not migrator.validator:
            print_status("Validator not available - PostgreSQL connection required", "ERROR")
            sys.exit(1)
        
        validation_tables = [
            "users", "apps", "configs", "memories", "categories",
            "access_controls", "archive_policies", "memory_status_history", "memory_access_logs"
        ]
        
        all_valid = True
        
        for table_name in validation_tables:
            print_status(f"Validating {table_name}...")
            
            # Count validation
            count_valid = migrator.validator.validate_table_counts(table_name)
            if not count_valid:
                all_valid = False
                continue
            
            # UUID validation
            uuid_columns = []
            if table_name in ["users", "apps", "memories", "categories"]:
                uuid_columns.append("id")
            if table_name == "apps":
                uuid_columns.append("owner_id")
            if table_name == "memories":
                uuid_columns.extend(["user_id", "app_id"])
            
            if uuid_columns:
                uuid_valid = migrator.validator.validate_uuid_integrity(table_name, uuid_columns)
                if not uuid_valid:
                    all_valid = False
        
        if all_valid:
            print_status("All validations passed!", "SUCCESS")
        else:
            print_status("Some validations failed!", "ERROR")
            sys.exit(1)
            
    except Exception as e:
        print_status(f"Validation failed: {e}", "ERROR")
        sys.exit(1)


def sync_command(args):
    """Manually sync specific records or tables"""
    print_status(f"Starting manual sync for {args.table}...")
    
    try:
        from app.models import User, App, Memory, Category
        
        model_mapping = {
            "users": User,
            "apps": App,
            "memories": Memory,
            "categories": Category
        }
        
        if args.table not in model_mapping:
            print_status(f"Unknown table: {args.table}", "ERROR")
            sys.exit(1)
        
        model_class = model_mapping[args.table]
        
        if args.record_id:
            # Sync specific record
            success = dual_write_manager.sync_record(model_class, args.record_id)
            if success:
                print_status(f"Successfully synced record {args.record_id}", "SUCCESS")
            else:
                print_status(f"Failed to sync record {args.record_id}", "ERROR")
                sys.exit(1)
        else:
            # Sync all records in table
            print_status(f"Syncing all records in {args.table}...")
            
            with dual_write_manager.get_session("sqlite") as session:
                records = session.query(model_class).all()
                total = len(records)
                synced = 0
                failed = 0
                
                for record in records:
                    if dual_write_manager.sync_record(model_class, record.id):
                        synced += 1
                    else:
                        failed += 1
                    
                    if (synced + failed) % 10 == 0:
                        print_status(f"Progress: {synced + failed}/{total} records processed")
                
                print_status(f"Sync completed: {synced} successful, {failed} failed", "SUCCESS")
        
    except Exception as e:
        print_status(f"Sync failed: {e}", "ERROR")
        sys.exit(1)


def backup_command(args):
    """Create backup of SQLite database"""
    print_status("Creating SQLite backup...")
    
    try:
        config = MigrationConfig()
        migrator = DatabaseMigrator(config)
        backup_path = migrator.backup_sqlite_database()
        print_status(f"Backup created: {backup_path}", "SUCCESS")
        
    except Exception as e:
        print_status(f"Backup failed: {e}", "ERROR")
        sys.exit(1)


def set_mode_command(args):
    """Set migration mode"""
    print_status(f"Setting migration mode to {args.mode}...")
    
    try:
        # Validate mode
        try:
            mode = MigrationMode(args.mode)
        except ValueError:
            print_status(f"Invalid mode: {args.mode}", "ERROR")
            print_status(f"Valid modes: {[m.value for m in MigrationMode]}")
            sys.exit(1)
        
        # Update environment variable (this would typically be done in a .env file)
        print_status(f"To set migration mode, update your .env file:")
        print_status(f"MIGRATION_MODE={args.mode}")
        
        # Show current configuration
        os.environ["MIGRATION_MODE"] = args.mode
        config = MigrationConfig()
        print_status(f"New configuration will be:")
        print_json({
            "migration_mode": config.mode.value,
            "write_to_sqlite": config.mode in [MigrationMode.SQLITE_ONLY, MigrationMode.DUAL_WRITE],
            "write_to_postgres": config.mode in [MigrationMode.DUAL_WRITE, MigrationMode.SUPABASE_ONLY]
        })
        
    except Exception as e:
        print_status(f"Mode setting failed: {e}", "ERROR")
        sys.exit(1)


def sync_categories_command(args):
    """Sync categories from PostgreSQL to SQLite"""
    print_status("Syncing categories from PostgreSQL to SQLite...")
    
    try:
        from app.models import Category
        
        print_status("Fetching categories from PostgreSQL...")
        with dual_write_manager.get_session("postgres") as pg_session:
            postgres_categories = pg_session.query(Category).all()
        
        print_status(f"Found {len(postgres_categories)} categories in PostgreSQL.")
        
        print_status("Fetching categories from SQLite...")
        with dual_write_manager.get_session("sqlite") as sqlite_session:
            from sqlalchemy import text
            result = sqlite_session.execute(text("SELECT name FROM categories")).fetchall()
            sqlite_category_names = {row[0] for row in result}
        
        print_status(f"Found {len(sqlite_category_names)} categories in SQLite.")
        
        new_categories = [
            cat for cat in postgres_categories if cat.name not in sqlite_category_names
        ]
        
        if not new_categories:
            print_status("Categories are already in sync.", "SUCCESS")
            return
            
        print_status(f"Found {len(new_categories)} new categories to sync.")
        
        with dual_write_manager.get_session("sqlite") as sqlite_session:
            from sqlalchemy import text
            for category in new_categories:
                sqlite_session.execute(
                    text("""
                        INSERT INTO categories (id, name, description, created_at, updated_at)
                        VALUES (:id, :name, :description, :created_at, :updated_at)
                    """),
                    {
                        "id": str(category.id),
                        "name": category.name,
                        "description": category.description,
                        "created_at": category.created_at,
                        "updated_at": category.updated_at,
                    },
                )
            sqlite_session.commit()
        
        print_status(f"Successfully synced {len(new_categories)} categories to SQLite.", "SUCCESS")

    except Exception as e:
        print_status(f"Category sync failed: {e}", "ERROR")
        sys.exit(1)


def health_command(args):
    """Check database health"""
    print_status("Checking database health...")
    
    try:
        health = dual_write_manager.check_connection_health()
        print_json(health)
        
        # Detailed health check
        sqlite_healthy = health.get("sqlite", {}).get("healthy", False)
        postgres_info = health.get("postgres")
        postgres_healthy = postgres_info.get("healthy", False) if postgres_info else None
        
        print_status(f"SQLite: {'Healthy' if sqlite_healthy else 'Unhealthy'}")
        if postgres_info:
            print_status(f"PostgreSQL: {'Healthy' if postgres_healthy else 'Unhealthy'}")
        else:
            print_status("PostgreSQL: Not configured")
        
        if not sqlite_healthy or (postgres_info and not postgres_healthy):
            sys.exit(1)
            
    except Exception as e:
        print_status(f"Health check failed: {e}", "ERROR")
        sys.exit(1)


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description="Database Migration CLI Tools")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Migration command
    migrate_parser = subparsers.add_parser("migrate", help="Run database migration")
    migrate_parser.add_argument("--dry-run", action="store_true", help="Show what would be migrated without executing")
    migrate_parser.set_defaults(func=migrate_command)
    
    # Rollback command
    rollback_parser = subparsers.add_parser("rollback", help="Rollback migration")
    rollback_parser.add_argument("--confirm", action="store_true", help="Skip confirmation prompt")
    rollback_parser.set_defaults(func=rollback_command)
    
    # Status command
    status_parser = subparsers.add_parser("status", help="Show migration status")
    status_parser.set_defaults(func=status_command)
    
    # Validate command
    validate_parser = subparsers.add_parser("validate", help="Validate data integrity")
    validate_parser.set_defaults(func=validate_command)
    
    # Sync command
    sync_parser = subparsers.add_parser("sync", help="Manually sync data")
    sync_parser.add_argument("table", choices=["users", "apps", "memories", "categories"], help="Table to sync")
    sync_parser.add_argument("--record-id", help="Specific record ID to sync")
    sync_parser.set_defaults(func=sync_command)
    
    # Backup command
    backup_parser = subparsers.add_parser("backup", help="Create SQLite backup")
    backup_parser.set_defaults(func=backup_command)
    
    # Set mode command
    mode_parser = subparsers.add_parser("set-mode", help="Set migration mode")
    mode_parser.add_argument("mode", choices=[m.value for m in MigrationMode], help="Migration mode")
    mode_parser.set_defaults(func=set_mode_command)
    
    # Health command
    health_parser = subparsers.add_parser("health", help="Check database health")
    health_parser.set_defaults(func=health_command)

    # Sync categories command
    sync_cat_parser = subparsers.add_parser("sync-categories", help="Sync categories from PostgreSQL to SQLite")
    sync_cat_parser.set_defaults(func=sync_categories_command)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    args.func(args)


if __name__ == "__main__":
    main()