"""
Database Service Layer
Provides high-level database operations with dual-write support
"""
import logging
from typing import List, Optional, Dict, Any, Tuple
from uuid import UUID
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc

from app.models import (
    User, App, Config, Memory, Category, AccessControl,
    ArchivePolicy, MemoryStatusHistory, MemoryAccessLog, memory_categories
)
from app.database.dual_write import dual_write_manager, WriteResult

logger = logging.getLogger(__name__)


class DatabaseService:
    """High-level database service with dual-write support"""
    
    def __init__(self):
        self.dual_write = dual_write_manager
    
    def _handle_write_result(self, primary_result: WriteResult, secondary_result: Optional[WriteResult] = None):
        """Handle write operation results and logging"""
        if not primary_result.success:
            logger.error(f"Primary database write failed: {primary_result.error}")
            raise Exception(f"Database operation failed: {primary_result.error}")
        
        if secondary_result and not secondary_result.success:
            logger.warning(f"Secondary database write failed: {secondary_result.error}")
            # Don't raise exception for secondary failures in dual-write mode
    
    # User operations
    def create_user(self, user_id: str, name: Optional[str] = None, 
                   email: Optional[str] = None, metadata: Optional[Dict] = None,
                   supabase_user_id: Optional[UUID] = None) -> User:
        """Create a new user"""
        primary_result, secondary_result = self.dual_write.create_record(
            User,
            user_id=user_id,
            name=name,
            email=email,
            metadata_=metadata or {},
            supabase_user_id=supabase_user_id
        )
        
        self._handle_write_result(primary_result, secondary_result)
        return primary_result.data
    
    def get_user(self, user_id: Optional[str] = None, id: Optional[UUID] = None) -> Optional[User]:
        """Get user by user_id or id"""
        with self.dual_write.get_session() as session:
            query = session.query(User)
            if user_id:
                query = query.filter(User.user_id == user_id)
            elif id:
                query = query.filter(User.id == id)
            else:
                return None
            return query.first()
    
    def update_user(self, user_id: UUID, **kwargs) -> User:
        """Update user"""
        primary_result, secondary_result = self.dual_write.update_record(
            User, user_id, **kwargs
        )
        
        self._handle_write_result(primary_result, secondary_result)
        return primary_result.data
    
    # App operations
    def create_app(self, owner_id: UUID, name: str, description: Optional[str] = None,
                   metadata: Optional[Dict] = None) -> App:
        """Create a new app"""
        primary_result, secondary_result = self.dual_write.create_record(
            App,
            owner_id=owner_id,
            name=name,
            description=description,
            metadata_=metadata or {}
        )
        
        self._handle_write_result(primary_result, secondary_result)
        return primary_result.data
    
    def get_app(self, app_id: Optional[UUID] = None, name: Optional[str] = None) -> Optional[App]:
        """Get app by id or name"""
        with self.dual_write.get_session() as session:
            query = session.query(App)
            if app_id:
                query = query.filter(App.id == app_id)
            elif name:
                query = query.filter(App.name == name)
            else:
                return None
            return query.first()
    
    def get_apps_for_user(self, user_id: UUID, is_active: bool = True) -> List[App]:
        """Get all apps for a user"""
        with self.dual_write.get_session() as session:
            query = session.query(App).filter(App.owner_id == user_id)
            if is_active is not None:
                query = query.filter(App.is_active == is_active)
            return query.all()
    
    def update_app(self, app_id: UUID, **kwargs) -> App:
        """Update app"""
        primary_result, secondary_result = self.dual_write.update_record(
            App, app_id, **kwargs
        )
        
        self._handle_write_result(primary_result, secondary_result)
        return primary_result.data
    
    # Memory operations
    def create_memory(self, user_id: UUID, app_id: UUID, content: str,
                     vector: Optional[str] = None, metadata: Optional[Dict] = None,
                     state: str = "active") -> Memory:
        """Create a new memory"""
        primary_result, secondary_result = self.dual_write.create_record(
            Memory,
            user_id=user_id,
            app_id=app_id,
            content=content,
            vector=vector,
            metadata_=metadata or {},
            state=state
        )
        
        self._handle_write_result(primary_result, secondary_result)
        return primary_result.data
    
    def get_memory(self, memory_id: UUID) -> Optional[Memory]:
        """Get memory by id"""
        with self.dual_write.get_session() as session:
            return session.query(Memory).filter(Memory.id == memory_id).first()
    
    def get_memories(self, user_id: Optional[UUID] = None, app_id: Optional[UUID] = None,
                    state: Optional[str] = None, limit: int = 100, offset: int = 0,
                    order_by: str = "created_at", order_desc: bool = True) -> List[Memory]:
        """Get memories with filtering and pagination"""
        with self.dual_write.get_session() as session:
            query = session.query(Memory)
            
            if user_id:
                query = query.filter(Memory.user_id == user_id)
            if app_id:
                query = query.filter(Memory.app_id == app_id)
            if state:
                query = query.filter(Memory.state == state)
            
            # Order by
            if hasattr(Memory, order_by):
                column = getattr(Memory, order_by)
                if order_desc:
                    query = query.order_by(desc(column))
                else:
                    query = query.order_by(asc(column))
            
            return query.offset(offset).limit(limit).all()
    
    def update_memory(self, memory_id: UUID, **kwargs) -> Memory:
        """Update memory"""
        primary_result, secondary_result = self.dual_write.update_record(
            Memory, memory_id, **kwargs
        )
        
        self._handle_write_result(primary_result, secondary_result)
        return primary_result.data
    
    def delete_memory(self, memory_id: UUID, soft_delete: bool = True) -> Memory:
        """Delete or archive memory"""
        if soft_delete:
            # Use update for soft delete
            return self.update_memory(
                memory_id,
                state="deleted",
                deleted_at=datetime.now(timezone.utc)
            )
        else:
            # Hard delete
            primary_result, secondary_result = self.dual_write.delete_record(
                Memory, memory_id, soft_delete=False
            )
            
            self._handle_write_result(primary_result, secondary_result)
            return primary_result.data
    
    def search_memories(self, user_id: UUID, query: str, app_id: Optional[UUID] = None,
                       limit: int = 50) -> List[Memory]:
        """Search memories by content"""
        with self.dual_write.get_session() as session:
            db_query = session.query(Memory).filter(
                and_(
                    Memory.user_id == user_id,
                    Memory.content.contains(query),
                    Memory.state == "active"
                )
            )
            
            if app_id:
                db_query = db_query.filter(Memory.app_id == app_id)
            
            return db_query.limit(limit).all()
    
    # Category operations
    def create_category(self, name: str, description: Optional[str] = None) -> Category:
        """Create a new category"""
        primary_result, secondary_result = self.dual_write.create_record(
            Category,
            name=name,
            description=description
        )
        
        self._handle_write_result(primary_result, secondary_result)
        return primary_result.data
    
    def get_category(self, category_id: Optional[UUID] = None, name: Optional[str] = None) -> Optional[Category]:
        """Get category by id or name"""
        with self.dual_write.get_session() as session:
            query = session.query(Category)
            if category_id:
                query = query.filter(Category.id == category_id)
            elif name:
                query = query.filter(Category.name == name)
            else:
                return None
            return query.first()
    
    def get_categories(self, limit: int = 100) -> List[Category]:
        """Get all categories"""
        with self.dual_write.get_session() as session:
            return session.query(Category).limit(limit).all()
    
    def add_memory_category(self, memory_id: UUID, category_id: UUID):
        """Add category to memory"""
        primary_result, secondary_result = self.dual_write.execute_raw_sql(
            """
            INSERT INTO memory_master.memory_categories (memory_id, category_id)
            VALUES (:memory_id, :category_id)
            ON CONFLICT DO NOTHING
            """,
            {"memory_id": str(memory_id), "category_id": str(category_id)}
        )
        
        self._handle_write_result(primary_result, secondary_result)
    
    def get_memory_categories(self, memory_id: UUID) -> List[Category]:
        """Get all categories for a memory"""
        with self.dual_write.get_session() as session:
            return session.query(Category).join(
                memory_categories
            ).filter(
                memory_categories.c.memory_id == memory_id
            ).all()
    
    # Config operations
    def set_config(self, key: str, value: Any) -> Config:
        """Set configuration value"""
        # Check if config exists
        with self.dual_write.get_session() as session:
            config = session.query(Config).filter(Config.key == key).first()
        
        if config:
            # Update existing
            primary_result, secondary_result = self.dual_write.update_record(
                Config, config.id, value=value
            )
        else:
            # Create new
            primary_result, secondary_result = self.dual_write.create_record(
                Config, key=key, value=value
            )
        
        self._handle_write_result(primary_result, secondary_result)
        return primary_result.data
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        with self.dual_write.get_session() as session:
            config = session.query(Config).filter(Config.key == key).first()
            return config.value if config else default
    
    # Statistics and reporting
    def get_user_stats(self, user_id: UUID) -> Dict[str, Any]:
        """Get user statistics"""
        with self.dual_write.get_session() as session:
            stats = {
                "total_memories": session.query(Memory).filter(
                    and_(Memory.user_id == user_id, Memory.state == "active")
                ).count(),
                "total_apps": session.query(App).filter(
                    and_(App.owner_id == user_id, App.is_active == True)
                ).count(),
                "archived_memories": session.query(Memory).filter(
                    and_(Memory.user_id == user_id, Memory.state == "archived")
                ).count(),
                "deleted_memories": session.query(Memory).filter(
                    and_(Memory.user_id == user_id, Memory.state == "deleted")
                ).count()
            }
            
            # Get latest memory
            latest_memory = session.query(Memory).filter(
                and_(Memory.user_id == user_id, Memory.state == "active")
            ).order_by(desc(Memory.created_at)).first()
            
            if latest_memory:
                stats["latest_memory_date"] = latest_memory.created_at
                stats["latest_memory_app"] = latest_memory.app.name if latest_memory.app else None
            
            return stats
    
    def get_app_stats(self, app_id: UUID) -> Dict[str, Any]:
        """Get app statistics"""
        with self.dual_write.get_session() as session:
            stats = {
                "total_memories": session.query(Memory).filter(
                    and_(Memory.app_id == app_id, Memory.state == "active")
                ).count(),
                "total_users": session.query(Memory.user_id).filter(
                    Memory.app_id == app_id
                ).distinct().count(),
                "archived_memories": session.query(Memory).filter(
                    and_(Memory.app_id == app_id, Memory.state == "archived")
                ).count()
            }
            
            # Get latest memory
            latest_memory = session.query(Memory).filter(
                and_(Memory.app_id == app_id, Memory.state == "active")
            ).order_by(desc(Memory.created_at)).first()
            
            if latest_memory:
                stats["latest_memory_date"] = latest_memory.created_at
            
            return stats
    
    # Migration and sync helpers
    def get_sync_status(self) -> Dict[str, Any]:
        """Get database synchronization status"""
        return self.dual_write.get_sync_status()
    
    def health_check(self) -> Dict[str, Any]:
        """Check database health"""
        return self.dual_write.check_connection_health()


# Global instance
db_service = DatabaseService()