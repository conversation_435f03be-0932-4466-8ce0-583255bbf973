#!/usr/bin/env python3
"""
Minimal test for concurrent operation handling without database dependencies.
"""

import threading
import time
import queue
from typing import Optional, Callable, Any, Tuple
from unittest.mock import Mock

class MinimalMemoryClientSingleton:
    """
    Minimal version of MemoryClientSingleton for testing concurrent operations.
    """
    _instance = None
    _lock = threading.Lock()
    _client = None
    
    # Concurrent operation handling
    _operation_lock = threading.Lock()
    _operation_queue = queue.Queue()
    _worker_thread = None
    _worker_running = False
    _operation_counter = 0
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def _start_worker(self):
        """Start background worker thread to process queued operations."""
        with self._operation_lock:
            if self._worker_thread is None or not self._worker_thread.is_alive():
                self._worker_running = True
                self._worker_thread = threading.Thread(target=self._process_queue, daemon=True)
                self._worker_thread.start()
                print("Memory operation worker thread started")
    
    def _stop_worker(self):
        """Stop the background worker thread."""
        with self._operation_lock:
            self._worker_running = False
            if self._worker_thread and self._worker_thread.is_alive():
                # Add a sentinel value to wake up the worker thread
                self._operation_queue.put((None, None, None, None))
                print("Memory operation worker thread stopping...")
    
    def _process_queue(self):
        """Process operations from the queue."""
        print("Memory operation worker thread started processing")
        while self._worker_running:
            try:
                # Get operation from queue with timeout
                operation, args, kwargs, result_callback = self._operation_queue.get(timeout=1.0)
                
                # Check for sentinel value (stop signal)
                if operation is None:
                    break
                
                operation_id = None
                try:
                    # Generate operation ID for tracking
                    with self._operation_lock:
                        self._operation_counter += 1
                        operation_id = self._operation_counter
                    
                    operation_name = getattr(operation, '__name__', str(operation))
                    print(f"Processing queued operation {operation_id}: {operation_name}")
                    
                    # Execute the operation with proper locking
                    with self._operation_lock:
                        result = operation(*args, **kwargs)
                    
                    # Call success callback if provided
                    if result_callback:
                        result_callback(True, result, operation_id)
                        
                    print(f"Queued operation {operation_id} completed successfully")
                    
                except Exception as e:
                    print(f"Queued operation {operation_id} failed: {str(e)}")
                    # Call error callback if provided
                    if result_callback:
                        result_callback(False, str(e), operation_id)
                finally:
                    self._operation_queue.task_done()
                    
            except queue.Empty:
                # Timeout occurred, continue loop to check _worker_running
                continue
            except Exception as e:
                print(f"Worker thread error: {str(e)}")
                
        print("Memory operation worker thread stopped")
    
    def add_memory_async(self, content: str, metadata: dict = None, callback: Callable[[bool, Any, int], None] = None) -> Tuple[bool, str]:
        """Queue a memory addition operation for asynchronous processing."""
        if self._client is None:
            return False, "Memory client not initialized"
            
        try:
            # Start worker thread if not running
            self._start_worker()
            
            # Queue the operation
            self._operation_queue.put((
                self._client.add,  # The actual mem0 add method
                (content,),        # args
                {"metadata": metadata} if metadata else {},  # kwargs
                callback           # result callback
            ))
            
            return True, "Memory addition queued for processing"
            
        except Exception as e:
            return False, f"Failed to queue memory operation: {str(e)}"
    
    def get_operation_queue_status(self) -> dict:
        """Get status information about the operation queue."""
        with self._operation_lock:
            return {
                "queue_size": self._operation_queue.qsize(),
                "worker_running": self._worker_running,
                "worker_alive": self._worker_thread.is_alive() if self._worker_thread else False,
                "operation_counter": self._operation_counter
            }
    
    def wait_for_queue_empty(self, timeout: float = 30.0) -> bool:
        """Wait for the operation queue to be empty."""
        try:
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                if self._operation_queue.empty():
                    return True
                time.sleep(0.1)
                
            return False
            
        except Exception as e:
            print(f"Error waiting for queue: {str(e)}")
            return False
    
    def set_mock_client(self, mock_client):
        """Set a mock client for testing."""
        self._client = mock_client

def test_concurrent_operations():
    """Test concurrent operations with minimal implementation."""
    print("Testing concurrent operations...")
    
    # Reset singleton
    MinimalMemoryClientSingleton._instance = None
    
    # Create singleton and set mock client
    singleton = MinimalMemoryClientSingleton()
    mock_client = Mock()
    mock_client.add.return_value = {"id": "test-id", "status": "success"}
    singleton.set_mock_client(mock_client)
    
    # Test basic functionality
    print("✓ Singleton created and mock client set")
    
    # Test queue status
    status = singleton.get_operation_queue_status()
    print(f"Initial queue status: {status}")
    assert status['queue_size'] == 0
    assert not status['worker_running']
    print("✓ Initial queue status correct")
    
    # Test async operation
    success, message = singleton.add_memory_async("test content", {"test": "metadata"})
    assert success, f"Operation should succeed: {message}"
    print("✓ Async operation queued successfully")
    
    # Wait for operation to complete
    assert singleton.wait_for_queue_empty(timeout=5.0), "Queue should become empty"
    print("✓ Operation completed")
    
    # Verify mock was called
    mock_client.add.assert_called_once()
    print("✓ Mock client was called correctly")
    
    # Test multiple concurrent operations
    print("\nTesting multiple concurrent operations...")
    
    results = []
    def callback(success, result, operation_id):
        results.append((success, result, operation_id))
    
    # Queue multiple operations
    for i in range(10):
        success, message = singleton.add_memory_async(f"content {i}", {"index": i}, callback)
        assert success, f"Operation {i} should be queued"
    
    # Wait for all operations to complete
    assert singleton.wait_for_queue_empty(timeout=10.0), "All operations should complete"
    
    # Wait for callbacks
    time.sleep(0.5)
    
    print(f"Completed {len(results)} operations")
    assert len(results) == 10, "All operations should have callbacks"
    
    # Stop worker
    singleton._stop_worker()
    time.sleep(0.2)
    
    status = singleton.get_operation_queue_status()
    assert not status['worker_running'], "Worker should be stopped"
    print("✓ Worker stopped correctly")
    
    print("🎉 All concurrent operation tests passed!")

if __name__ == "__main__":
    test_concurrent_operations()
