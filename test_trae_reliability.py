#!/usr/bin/env python3
"""
Test script to simulate Trae AI IDE behavior - rapid submission of multiple short text memories.
This will help us identify the reliability issues <PERSON><PERSON> is experiencing.
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://************:8765"
USER_ID = "aungheinaye"
CLIENT_NAME = "trae"
NUM_MEMORIES = 52  # Same as what <PERSON><PERSON> tried
CONCURRENT_BATCHES = 4  # Simulate some concurrency

# Sample short text memories (similar to what an IDE might save)
SAMPLE_MEMORIES = [
    "User prefers using async/await syntax over callbacks",
    "Project uses TypeScript with strict mode enabled",
    "<PERSON><PERSON><PERSON> likes to use descriptive variable names",
    "Team follows ESLint rules with no semicolons",
    "User prefers arrow functions over traditional functions",
    "Project structure follows clean architecture principles",
    "<PERSON><PERSON><PERSON> uses VSCode with Prettier formatting",
    "Team uses Git with conventional commit messages",
    "User prefers functional programming patterns",
    "Project uses React with hooks instead of classes",
    "<PERSON><PERSON><PERSON> likes to write unit tests for all functions",
    "Team uses Docker for local development",
    "User prefers const over let when possible",
    "Project uses Tailwind CSS for styling",
    "<PERSON><PERSON><PERSON> likes to use object destructuring",
    "Team follows semantic versioning",
    "User prefers early returns to avoid deep nesting",
    "Project uses PostgreSQL as primary database",
    "Developer likes to use TypeScript interfaces",
    "Team uses CI/CD with GitHub Actions",
]

async def add_memory_via_mcp(session, memory_text, memory_id):
    """Add a memory via the MCP endpoint."""
    url = f"{BASE_URL}/mcp/{CLIENT_NAME}/sse/{USER_ID}"
    
    # MCP message format for adding memory
    mcp_message = {
        "jsonrpc": "2.0",
        "id": memory_id,
        "method": "tools/call",
        "params": {
            "name": "add_memories",
            "arguments": {
                "text": memory_text
            }
        }
    }
    
    try:
        start_time = time.time()
        async with session.post(url, json=mcp_message, timeout=30) as response:
            duration = time.time() - start_time
            
            if response.status == 200:
                result = await response.json()
                print(f"[{memory_id:2d}] ✓ Success in {duration:.3f}s: {memory_text[:50]}...")
                return True, duration, result
            else:
                text = await response.text()
                print(f"[{memory_id:2d}] ✗ HTTP {response.status} in {duration:.3f}s: {text[:100]}")
                return False, duration, text
                
    except asyncio.TimeoutError:
        duration = time.time() - start_time
        print(f"[{memory_id:2d}] ✗ Timeout after {duration:.3f}s: {memory_text[:50]}...")
        return False, duration, "Timeout"
    except Exception as e:
        duration = time.time() - start_time
        print(f"[{memory_id:2d}] ✗ Error after {duration:.3f}s: {e}")
        return False, duration, str(e)

async def run_batch(session, batch_memories):
    """Run a batch of memory additions concurrently."""
    tasks = []
    for memory_id, memory_text in batch_memories:
        task = add_memory_via_mcp(session, memory_text, memory_id)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results

async def main():
    print(f"🚀 Starting Trae AI IDE reliability test")
    print(f"   Target: {BASE_URL}")
    print(f"   User: {USER_ID}")
    print(f"   Client: {CLIENT_NAME}")
    print(f"   Memories: {NUM_MEMORIES}")
    print(f"   Concurrent batches: {CONCURRENT_BATCHES}")
    print(f"   Start time: {datetime.now().isoformat()}")
    print("-" * 80)
    
    # Prepare memories
    memories = []
    for i in range(NUM_MEMORIES):
        memory_text = f"{SAMPLE_MEMORIES[i % len(SAMPLE_MEMORIES)]} (test #{i+1})"
        memories.append((i+1, memory_text))
    
    # Split into batches for concurrent processing
    batch_size = len(memories) // CONCURRENT_BATCHES
    batches = []
    for i in range(0, len(memories), batch_size):
        batch = memories[i:i + batch_size]
        batches.append(batch)
    
    # Run the test
    async with aiohttp.ClientSession() as session:
        overall_start = time.time()
        
        # Process batches concurrently
        batch_tasks = []
        for batch_id, batch in enumerate(batches):
            print(f"📦 Batch {batch_id + 1}: {len(batch)} memories")
            task = run_batch(session, batch)
            batch_tasks.append(task)
        
        # Wait for all batches to complete
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
        
        overall_duration = time.time() - overall_start
        
        # Analyze results
        total_success = 0
        total_failed = 0
        total_duration_sum = 0
        min_duration = float('inf')
        max_duration = 0
        
        for batch_result in batch_results:
            if isinstance(batch_result, Exception):
                print(f"Batch failed with exception: {batch_result}")
                continue
                
            for result in batch_result:
                if isinstance(result, Exception):
                    total_failed += 1
                    print(f"Individual request failed: {result}")
                    continue
                    
                success, duration, response = result
                total_duration_sum += duration
                min_duration = min(min_duration, duration)
                max_duration = max(max_duration, duration)
                
                if success:
                    total_success += 1
                else:
                    total_failed += 1
        
        # Print summary
        print("-" * 80)
        print(f"📊 RESULTS SUMMARY")
        print(f"   Total memories attempted: {NUM_MEMORIES}")
        print(f"   Successful: {total_success}")
        print(f"   Failed: {total_failed}")
        print(f"   Success rate: {(total_success/NUM_MEMORIES)*100:.1f}%")
        print(f"   Overall duration: {overall_duration:.3f}s")
        
        if total_success > 0:
            avg_duration = total_duration_sum / (total_success + total_failed)
            print(f"   Average request time: {avg_duration:.3f}s")
            print(f"   Min request time: {min_duration:.3f}s")
            print(f"   Max request time: {max_duration:.3f}s")
        
        print(f"   End time: {datetime.now().isoformat()}")
        
        if total_failed > 0:
            print(f"\n❌ RELIABILITY ISSUE DETECTED:")
            print(f"   {total_failed}/{NUM_MEMORIES} memories failed to save")
            print(f"   This matches the Trae AI IDE issue reported")
            print(f"\n🔍 Check logs for detailed failure analysis:")
            print(f"   docker logs memory-mcp")
            print(f"   cat /tmp/debug_add_memories.log")
        else:
            print(f"\n✅ All memories saved successfully")

if __name__ == "__main__":
    asyncio.run(main())